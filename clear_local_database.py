#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import yaml
import os

def load_config():
    """加载配置文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, 'config-local-ue5fishing.yaml')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def clear_database():
    """清空数据库中的相关表"""
    config = load_config()
    mysql_config = config['mysql']
    
    print("正在连接数据库...")
    conn = pymysql.connect(**mysql_config)
    
    try:
        with conn.cursor() as cursor:
            # 获取所有表名
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            
            print(f"发现以下表: {table_names}")
            
            # 清空相关表
            tables_to_clear = [
                'ad_intervention_history',  # 广告干预历史表
                'service_status'           # 服务状态表
            ]
            
            for table in tables_to_clear:
                if table in table_names:
                    print(f"正在清空表: {table}")
                    cursor.execute(f"DELETE FROM {table}")
                    print(f"表 {table} 已清空，删除了 {cursor.rowcount} 条记录")
                else:
                    print(f"表 {table} 不存在，跳过")
            
            # 提交更改
            conn.commit()
            print("数据库清空完成！")
            
    except Exception as e:
        print(f"清空数据库时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    clear_database() 