{"cells": [{"cell_type": "markdown", "id": "fbb6ad3c", "metadata": {}, "source": ["## 读取数据"]}, {"cell_type": "code", "execution_count": 1, "id": "7cdf0a37", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', None)\n", "\n", "operation_file_path = \"/home/<USER>/operations/data/operation_logs_20250519.csv\"\n", "operation_df = pd.read_csv(operation_file_path, on_bad_lines='skip')\n", "operation_df['operation_time'] = operation_df['operation_time'].str.strip()\n", "\n", "adv_snapshot_file_path = \"/home/<USER>/operations/data/realtime_snapshot_3d.csv\"\n", "adv_snapshot_df = pd.read_csv(adv_snapshot_file_path, on_bad_lines='skip')\n", "adv_snapshot_df = adv_snapshot_df.drop_duplicates(subset=['snap_day', 'snap_time', 'ad_id'])\n", "adv_snapshot_df = adv_snapshot_df.drop(columns=['media_source', 'sub_job_id', 'execute_date'], errors='ignore')\n", "\n", "adv_history_file_path = \"/home/<USER>/operations/data/adv_history.csv\"\n", "adv_history_df = pd.read_csv(adv_history_file_path, on_bad_lines='skip')\n", "\n", "studio_history_7_path = \"/home/<USER>/operations/data/studio_history_7.csv\"\n", "studio_history_7_df = pd.read_csv(studio_history_7_path, on_bad_lines='skip')\n", "studio_history_7_df = studio_history_7_df.drop(columns=['studio_id', 'studio_name'], errors='ignore')\n", "\n", "studio_history_30_path = \"/home/<USER>/operations/data/studio_history_30.csv\"\n", "studio_history_30_df = pd.read_csv(studio_history_30_path, on_bad_lines='skip')\n", "studio_history_30_df = studio_history_30_df.drop(columns=['studio_id', 'studio_name'], errors='ignore')\n", "\n", "# 这里需要修改snap_df中的时区\n", "adv_snapshot_df['datetime'] = pd.to_datetime(adv_snapshot_df['snap_day'].astype(str) + ' ' + adv_snapshot_df['snap_time'].astype(str))\n", "adv_snapshot_df['datetime'] = adv_snapshot_df['datetime'] + pd.Timedelta(hours=8)\n", "adv_snapshot_df['snap_day'] = adv_snapshot_df['datetime'].dt.date.astype(str)\n", "adv_snapshot_df['snap_time'] = adv_snapshot_df['datetime'].dt.time.astype(str)\n", "adv_snapshot_df.drop('datetime', axis=1, inplace=True)"]}, {"cell_type": "markdown", "id": "eda32d03", "metadata": {}, "source": ["## 构建通用数据"]}, {"cell_type": "code", "execution_count": 2, "id": "c9dac2ee", "metadata": {}, "outputs": [], "source": ["# 修改列名防止后续冲突\n", "adv_history_df.columns = [col + '_adv7' for col in adv_history_df.columns]\n", "studio_history_7_df.columns = [col + '_studio7' for col in studio_history_7_df.columns]\n", "studio_history_30_df.columns = [col + '_studio30' for col in studio_history_30_df.columns]\n", "\n", "# 整合广告快照数据和广告历史数据\n", "\n", "# [bug] 这里需要解决有的数据前后会有1s的时间间隔\n", "adv_snapshot_df['timestamp'] = pd.to_datetime(adv_snapshot_df['snap_day'].astype(str) + ' ' + adv_snapshot_df['snap_time'].astype(str))\n", "adv_snapshot_df = adv_snapshot_df.sort_values('timestamp')\n", "adv_snapshot_df['time_diff'] = adv_snapshot_df['timestamp'].diff().dt.total_seconds()\n", "adv_snapshot_df['group'] = (adv_snapshot_df['time_diff'] > 1).cumsum()\n", "adv_snapshot_df['timestamp'] = adv_snapshot_df.groupby('group')['timestamp'].transform('first')\n", "adv_snapshot_df['snap_time'] = adv_snapshot_df['timestamp'].dt.time\n", "adv_snapshot_df = adv_snapshot_df.drop(['time_diff', 'group', 'timestamp'], axis=1)\n", "\n", "adv_df = adv_snapshot_df.merge(\n", "    adv_history_df, \n", "    left_on=['ad_id', 'snap_day'], \n", "    right_on=['ad_id_adv7', 'date_adv7'], \n", "    how='left'\n", ").drop(columns=['ad_id_adv7', 'date_adv7'], errors='ignore').fillna(0)\n", "\n", "# 整合大盘快照数据和大盘历史数据\n", "\n", "# 大盘快照数据聚合\n", "market_snapshot_df = adv_snapshot_df.groupby(['snap_day', 'snap_time']).agg({\n", "    'ad_activation': 'sum',\n", "    'ad_c': 'sum',\n", "    'ad_i': 'sum',\n", "    'ad_pay1': 'sum',\n", "    'ad_pay_user': 'sum',\n", "    'ad_s': 'sum'\n", "}).reset_index()\n", "\n", "market_snapshot_df = market_snapshot_df.rename(columns={\n", "    'ad_activation': 'total_activation',\n", "    'ad_c': 'total_c',\n", "    'ad_i': 'total_i',\n", "    'ad_pay1': 'total_pay1',\n", "    'ad_pay_user': 'total_pay_user',\n", "    'ad_s': 'total_s'\n", "})\n", "market_snapshot_df['total_roi1'] = market_snapshot_df['total_pay1'].div(market_snapshot_df['total_s']).replace([np.inf, -np.inf], 0)\n", "\n", "# 聚合大盘历史数据（7日和30日）\n", "studio_df = pd.merge(\n", "    studio_history_7_df,\n", "    studio_history_30_df,\n", "    left_on=['date_studio7'],\n", "    right_on=['date_studio30'], \n", "    how='left'\n", ").drop('date_studio30', axis=1)\n", "date_column = studio_df.pop('date_studio7')\n", "studio_df.insert(0, 'date', date_column)\n", "\n", "# 整合大盘快照数据和大盘历史数据\n", "market_snapshot_df_complete = pd.merge(\n", "    market_snapshot_df,\n", "    studio_df,\n", "    left_on=['snap_day'],\n", "    right_on=['date'],\n", "    how='left'\n", ").drop('date', axis=1)"]}, {"cell_type": "markdown", "id": "9a07c754", "metadata": {}, "source": ["## 数据集构建"]}, {"cell_type": "code", "execution_count": null, "id": "1af9a3a9", "metadata": {}, "outputs": [], "source": ["operation_df_adv = operation_df[(operation_df['operation_target'] == '广告') & (~operation_df['operation_log'].isna())]"]}, {"cell_type": "code", "execution_count": 37, "id": "15f777bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["54766\n"]}], "source": ["operation_df_adv = operation_df_adv[\n", "    (~operation_df_adv['operation_log'].str.contains(\"审核\")) &\n", "    (~operation_df_adv['operation_log'].str.contains(\"产品卖点\")) &\n", "    (~operation_df_adv['operation_log'].str.contains(\"修改 素材状态\"))\n", "]\n", "print(operation_df_adv.shape[0])\n", "# operation_df_adv['operation_log'].value_counts()"]}, {"cell_type": "code", "execution_count": 38, "id": "d3879a93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3711\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2468/298409619.py:17: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_roi['macro_action'] = '修改ROI系数'\n", "/tmp/ipykernel_2468/298409619.py:18: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_roi['micro_action'] = operation_df_adv_roi['operation_log'].apply(get_roi_change)\n"]}], "source": ["import re\n", "\n", "def get_roi_change(log):\n", "    if isinstance(log, str) and '广告ROI系数:' in log:\n", "        # 提取数字\n", "        numbers = re.findall(r'(\\d+\\.\\d+)', log)\n", "        if len(numbers) >= 2:\n", "            old_roi = float(numbers[0])\n", "            new_roi = float(numbers[1])\n", "            if new_roi > old_roi:\n", "                return \"提高ROI系数\"\n", "            elif new_roi < old_roi:\n", "                return \"降低ROI系数\"\n", "    return None\n", "\n", "operation_df_adv_roi = operation_df_adv[operation_df_adv['operation_log'].str.contains(\"修改 广告ROI系数\")]\n", "operation_df_adv_roi['macro_action'] = '修改ROI系数'\n", "operation_df_adv_roi['micro_action'] = operation_df_adv_roi['operation_log'].apply(get_roi_change)\n", "operation_df_adv_roi.sort_values(['operation_day', 'operation_time'])\n", "\n", "print(operation_df_adv_roi.shape[0])\n", "# operation_df_adv_roi.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "fdd8aaff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1790\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1752/2872470403.py:15: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_bid['macro_action'] = '修改出价'\n", "/tmp/ipykernel_1752/2872470403.py:16: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_bid['micro_action'] = operation_df_adv_bid['operation_log'].apply(get_bid_change)\n"]}], "source": ["def get_bid_change(log):\n", "    if isinstance(log, str) and '广告出价:' in log:\n", "        # 提取数字\n", "        numbers = re.findall(r'(\\d+\\.\\d+)', log)\n", "        if len(numbers) >= 2:\n", "            old_bid = float(numbers[0])\n", "            new_bid = float(numbers[1])\n", "            if new_bid > old_bid:\n", "                return \"提高出价\"\n", "            elif new_bid < old_bid:\n", "                return \"降低出价\"\n", "    return None\n", "\n", "operation_df_adv_bid = operation_df_adv[operation_df_adv['operation_log'].str.contains(\"修改 广告出价\")]\n", "operation_df_adv_bid['macro_action'] = '修改出价'\n", "operation_df_adv_bid['micro_action'] = operation_df_adv_bid['operation_log'].apply(get_bid_change)\n", "print(operation_df_adv_bid.shape[0])\n", "# operation_df_adv_bid.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "21e0cdf2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1752/436197878.py:15: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_budget['macro_action'] = '修改预算'\n", "/tmp/ipykernel_1752/436197878.py:16: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_budget['micro_action'] = operation_df_adv_budget['operation_log'].apply(get_budget_change)\n"]}], "source": ["def get_budget_change(log):\n", "    if isinstance(log, str) and '广告预算:' in log:\n", "        # 提取数字\n", "        numbers = re.findall(r'(\\d+\\.\\d+)', log)\n", "        if len(numbers) >= 2:\n", "            old_budget = float(numbers[0])\n", "            new_budget = float(numbers[1])\n", "            if new_budget > old_budget:\n", "                return \"提高预算\"\n", "            elif new_budget < old_budget:\n", "                return \"降低预算\"\n", "    return None\n", "\n", "operation_df_adv_budget = operation_df_adv[operation_df_adv['operation_log'].str.contains(\"修改 广告预算\")]\n", "operation_df_adv_budget['macro_action'] = '修改预算'\n", "operation_df_adv_budget['micro_action'] = operation_df_adv_budget['operation_log'].apply(get_budget_change)\n", "print(operation_df_adv_budget.shape[0])\n", "# operation_df_adv_budget.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "8b406511", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1752/2492307010.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_stop['macro_action'] = '暂停广告'\n", "/tmp/ipykernel_1752/2492307010.py:4: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_start['macro_action'] = '重启广告'\n"]}, {"data": {"text/plain": ["(39428, 1451)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["operation_df_adv_stop = operation_df_adv[operation_df_adv['operation_log'] == \"[修改 操作状态: 正常 -> 暂停]\"]\n", "operation_df_adv_start = operation_df_adv[operation_df_adv['operation_log'] == \"[修改 操作状态: 暂停 -> 正常]\"]\n", "operation_df_adv_stop['macro_action'] = '暂停广告'\n", "operation_df_adv_start['macro_action'] = '重启广告'\n", "operation_df_adv_stop.shape[0], operation_df_adv_start.shape[0]"]}, {"cell_type": "code", "execution_count": 13, "id": "69f1b416", "metadata": {}, "outputs": [{"data": {"text/plain": ["optimizer_name\n", "王雅涛    25614\n", "刘雨晴    11739\n", "刘天宇     8842\n", "李柏玉      185\n", "Name: count, dtype: int64"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["actions = pd.concat([\n", "    operation_df_adv_stop,\n", "    operation_df_adv_start,\n", "    operation_df_adv_bid,\n", "    operation_df_adv_roi\n", "], axis=0)\n", "actions.shape[0]\n", "actions['optimizer_name'].value_counts()"]}, {"cell_type": "code", "execution_count": 14, "id": "99ab3369", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1752/2558200903.py:6: PerformanceWarning: dropping on a non-lexsorted multi-index without a level parameter may impact performance.\n", "  action_result = actions_sorted.groupby(['optimizer_name', 'action_group']).agg({\n"]}], "source": ["actions['timestamp'] = pd.to_datetime(actions['operation_day'].astype(str) + ' ' + actions['operation_time'].astype(str))\n", "actions_sorted = actions.sort_values(['optimizer_name', 'timestamp'])\n", "actions_sorted['time_diff'] = actions_sorted.groupby('optimizer_name')['timestamp'].diff().dt.total_seconds()\n", "actions_sorted['action_group'] = (actions_sorted['time_diff'] > 600).astype(int).cumsum()\n", "\n", "action_result = actions_sorted.groupby(['optimizer_name', 'action_group']).agg({\n", "    'macro_action': 'first',\n", "    'timestamp': ['first', 'last', 'count']\n", "}).reset_index().drop('action_group', axis=1)"]}, {"cell_type": "markdown", "id": "059aa2df", "metadata": {}, "source": ["## 单独分析关停广告的宏观操作"]}, {"cell_type": "code", "execution_count": 8, "id": "3a913427", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3176/1995747797.py:5: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_stop['macro_action'] = '暂停广告'\n", "/tmp/ipykernel_3176/1995747797.py:6: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_start['macro_action'] = '重启广告'\n"]}, {"data": {"text/plain": ["(39428, 1451)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["operation_df_adv = operation_df[(operation_df['operation_target'] == '广告') & (~operation_df['operation_log'].isna())]\n", "\n", "operation_df_adv_stop = operation_df_adv[operation_df_adv['operation_log'] == \"[修改 操作状态: 正常 -> 暂停]\"]\n", "operation_df_adv_start = operation_df_adv[operation_df_adv['operation_log'] == \"[修改 操作状态: 暂停 -> 正常]\"]\n", "operation_df_adv_stop['macro_action'] = '暂停广告'\n", "operation_df_adv_start['macro_action'] = '重启广告'\n", "operation_df_adv_stop.shape[0], operation_df_adv_start.shape[0]"]}, {"cell_type": "code", "execution_count": 9, "id": "25131a1a", "metadata": {}, "outputs": [], "source": ["# 合并动作和广告召回数据\n", "stop_actions = operation_df_adv_stop.copy()\n", "stop_actions['timestamp'] = pd.to_datetime(stop_actions['operation_day'].astype(str) + ' ' + stop_actions['operation_time'].astype(str))\n", "adv_snapshot_df['timestamp'] = pd.to_datetime(adv_snapshot_df['snap_day'].astype(str) + ' ' + adv_snapshot_df['snap_time'].astype(str))\n", "\n", "adv_snapshot_df = adv_snapshot_df.sort_values('timestamp')\n", "stop_actions = stop_actions.sort_values('timestamp')\n", "\n", "stop_actions_background = pd.merge_asof(\n", "    stop_actions,\n", "    adv_snapshot_df,\n", "    left_on='timestamp',\n", "    right_on='timestamp',\n", "    left_by='operation_object_id',\n", "    right_by='ad_id',\n", "    direction='backward'\n", ")\n", "\n", "# 这里需要注意不能用很久以前的快照数据作为当前操作的背景，因此需要筛选掉一批当天没有快照数据的广告\n", "stop_actions_background = stop_actions_background[stop_actions_background['snap_day'].astype(str) == stop_actions_background['operation_day'].astype(str)]"]}, {"cell_type": "code", "execution_count": 45, "id": "418743ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["4401"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["stop_actions_background.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "e8fc2970", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1752/2669062568.py:6: PerformanceWarning: dropping on a non-lexsorted multi-index without a level parameter may impact performance.\n", "  stop_action_result = stop_actions_sorted.groupby(['optimizer_name', 'action_group']).agg({\n"]}], "source": ["stop_actions_sorted = stop_actions_background.sort_values(['optimizer_name', 'timestamp'])\n", "stop_actions_sorted['time_diff'] = stop_actions_sorted.groupby('optimizer_name')['timestamp'].diff().dt.total_seconds()\n", "optimizer_change = (stop_actions_sorted['optimizer_name'] != stop_actions_sorted['optimizer_name'].shift()).astype(int)\n", "stop_actions_sorted['action_group'] =  ((stop_actions_sorted['time_diff'] > 600) | (stop_actions_sorted['optimizer_name'] != stop_actions_sorted['optimizer_name'].shift())).astype(int).cumsum()\n", "\n", "stop_action_result = stop_actions_sorted.groupby(['optimizer_name', 'action_group']).agg({\n", "    'macro_action': 'first',\n", "    'timestamp': ['first', 'last', 'count'],\n", "    'ad_activation': ['sum', 'max', 'min'],\n", "    'ad_c': ['sum', 'max', 'min'],\n", "    'ad_i': ['sum', 'max', 'min'],\n", "    'ad_pay1': ['sum', 'max', 'min'],\n", "    'ad_pay_user': ['sum', 'max', 'min'],\n", "    'ad_roi1': ['sum', 'max', 'min'],\n", "    'ad_roi_bid': ['sum', 'max', 'min'],\n", "    'ad_s': ['sum', 'max', 'min']\n", "}).reset_index().drop('action_group', axis=1)"]}, {"cell_type": "code", "execution_count": 33, "id": "3137122d", "metadata": {}, "outputs": [], "source": ["stop_action_result.columns = ['_'.join(col).strip('_') if isinstance(col, tuple) else col for col in stop_action_result.columns]"]}, {"cell_type": "code", "execution_count": 35, "id": "d1f93447", "metadata": {}, "outputs": [{"data": {"text/plain": ["0   2025-03-20 10:04:23\n", "1   2025-03-20 18:53:22\n", "2   2025-03-21 11:23:08\n", "3   2025-03-21 16:55:56\n", "4   2025-03-22 10:15:22\n", "Name: timestamp_first, dtype: datetime64[ns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["stop_action_result['timestamp_first'].head()"]}, {"cell_type": "code", "execution_count": 46, "id": "dff95209", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>optimizer_name</th>\n", "      <th>macro_action_first</th>\n", "      <th>start_timestamp</th>\n", "      <th>end_timestamp</th>\n", "      <th>timestamp_count</th>\n", "      <th>ad_activation_sum</th>\n", "      <th>ad_activation_max</th>\n", "      <th>ad_activation_min</th>\n", "      <th>ad_c_sum</th>\n", "      <th>ad_c_max</th>\n", "      <th>ad_c_min</th>\n", "      <th>ad_i_sum</th>\n", "      <th>ad_i_max</th>\n", "      <th>ad_i_min</th>\n", "      <th>ad_pay1_sum</th>\n", "      <th>ad_pay1_max</th>\n", "      <th>ad_pay1_min</th>\n", "      <th>ad_pay_user_sum</th>\n", "      <th>ad_pay_user_max</th>\n", "      <th>ad_pay_user_min</th>\n", "      <th>ad_roi1_sum</th>\n", "      <th>ad_roi1_max</th>\n", "      <th>ad_roi1_min</th>\n", "      <th>ad_roi_bid_sum</th>\n", "      <th>ad_roi_bid_max</th>\n", "      <th>ad_roi_bid_min</th>\n", "      <th>ad_s_sum</th>\n", "      <th>ad_s_max</th>\n", "      <th>ad_s_min</th>\n", "      <th>snap_timestamp</th>\n", "      <th>snap_day</th>\n", "      <th>snap_time</th>\n", "      <th>total_activation</th>\n", "      <th>total_c</th>\n", "      <th>total_i</th>\n", "      <th>total_pay1</th>\n", "      <th>total_pay_user</th>\n", "      <th>total_s</th>\n", "      <th>total_roi1</th>\n", "      <th>cost_studio7</th>\n", "      <th>show_studio7</th>\n", "      <th>click_studio7</th>\n", "      <th>cost_by_thousand_show_studio7</th>\n", "      <th>new_user_studio7</th>\n", "      <th>new_paid_user_studio7</th>\n", "      <th>new_user_cost_studio7</th>\n", "      <th>new_paid_user_cost_studio7</th>\n", "      <th>roi1_studio7</th>\n", "      <th>roi7_studio7</th>\n", "      <th>convert_studio7</th>\n", "      <th>active_studio7</th>\n", "      <th>convert_cost_studio7</th>\n", "      <th>active_cost_studio7</th>\n", "      <th>ctr_studio7</th>\n", "      <th>cvr_studio7</th>\n", "      <th>new_paid_rate_studio7</th>\n", "      <th>roi3_studio7</th>\n", "      <th>pay1_studio7</th>\n", "      <th>pay3_studio7</th>\n", "      <th>pay7_studio7</th>\n", "      <th>total_roi_studio7</th>\n", "      <th>total_pay_studio7</th>\n", "      <th>cost_studio30</th>\n", "      <th>show_studio30</th>\n", "      <th>click_studio30</th>\n", "      <th>cost_by_thousand_show_studio30</th>\n", "      <th>new_user_studio30</th>\n", "      <th>new_paid_user_studio30</th>\n", "      <th>new_user_cost_studio30</th>\n", "      <th>new_paid_user_cost_studio30</th>\n", "      <th>roi1_studio30</th>\n", "      <th>roi7_studio30</th>\n", "      <th>convert_studio30</th>\n", "      <th>active_studio30</th>\n", "      <th>convert_cost_studio30</th>\n", "      <th>active_cost_studio30</th>\n", "      <th>ctr_studio30</th>\n", "      <th>cvr_studio30</th>\n", "      <th>new_paid_rate_studio30</th>\n", "      <th>roi3_studio30</th>\n", "      <th>pay1_studio30</th>\n", "      <th>pay3_studio30</th>\n", "      <th>pay7_studio30</th>\n", "      <th>total_roi_studio30</th>\n", "      <th>total_pay_studio30</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>刘雨晴</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 14:11:31</td>\n", "      <td>2025-02-25 14:12:27</td>\n", "      <td>3</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>230.0</td>\n", "      <td>176.0</td>\n", "      <td>3.0</td>\n", "      <td>28518.0</td>\n", "      <td>21386.0</td>\n", "      <td>782.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>936.61</td>\n", "      <td>552.89</td>\n", "      <td>33.41</td>\n", "      <td>2025-02-25 13:52:09</td>\n", "      <td>2025-02-25</td>\n", "      <td>13:52:09</td>\n", "      <td>546</td>\n", "      <td>26050</td>\n", "      <td>2888030</td>\n", "      <td>764</td>\n", "      <td>21</td>\n", "      <td>94344.48</td>\n", "      <td>0.008098</td>\n", "      <td>1250299.85</td>\n", "      <td>38566029</td>\n", "      <td>320331</td>\n", "      <td>32.42</td>\n", "      <td>5648</td>\n", "      <td>240</td>\n", "      <td>221.37</td>\n", "      <td>5209.58</td>\n", "      <td>0.0109</td>\n", "      <td>0.0684</td>\n", "      <td>419</td>\n", "      <td>7320</td>\n", "      <td>2984.01</td>\n", "      <td>170.81</td>\n", "      <td>0.0083</td>\n", "      <td>0.0176</td>\n", "      <td>0.0425</td>\n", "      <td>0.0325</td>\n", "      <td>13572</td>\n", "      <td>40632</td>\n", "      <td>85496</td>\n", "      <td>0.371</td>\n", "      <td>463878</td>\n", "      <td>8137807.04</td>\n", "      <td>278040286</td>\n", "      <td>2204652</td>\n", "      <td>29.27</td>\n", "      <td>24149</td>\n", "      <td>1222</td>\n", "      <td>336.98</td>\n", "      <td>6659.42</td>\n", "      <td>0.0114</td>\n", "      <td>0.0679</td>\n", "      <td>3258</td>\n", "      <td>32718</td>\n", "      <td>2497.79</td>\n", "      <td>248.73</td>\n", "      <td>0.0079</td>\n", "      <td>0.011</td>\n", "      <td>0.0506</td>\n", "      <td>0.0327</td>\n", "      <td>92724</td>\n", "      <td>265868</td>\n", "      <td>552790</td>\n", "      <td>0.3745</td>\n", "      <td>3047444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>刘雨晴</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 15:23:48</td>\n", "      <td>2025-02-25 15:24:38</td>\n", "      <td>2</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>176.0</td>\n", "      <td>176.0</td>\n", "      <td>0.0</td>\n", "      <td>21496.0</td>\n", "      <td>21429.0</td>\n", "      <td>67.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>554.19</td>\n", "      <td>553.08</td>\n", "      <td>1.11</td>\n", "      <td>2025-02-25 15:13:12</td>\n", "      <td>2025-02-25</td>\n", "      <td>15:13:12</td>\n", "      <td>651</td>\n", "      <td>29181</td>\n", "      <td>3223321</td>\n", "      <td>4658</td>\n", "      <td>22</td>\n", "      <td>107582.40</td>\n", "      <td>0.043297</td>\n", "      <td>1250299.85</td>\n", "      <td>38566029</td>\n", "      <td>320331</td>\n", "      <td>32.42</td>\n", "      <td>5648</td>\n", "      <td>240</td>\n", "      <td>221.37</td>\n", "      <td>5209.58</td>\n", "      <td>0.0109</td>\n", "      <td>0.0684</td>\n", "      <td>419</td>\n", "      <td>7320</td>\n", "      <td>2984.01</td>\n", "      <td>170.81</td>\n", "      <td>0.0083</td>\n", "      <td>0.0176</td>\n", "      <td>0.0425</td>\n", "      <td>0.0325</td>\n", "      <td>13572</td>\n", "      <td>40632</td>\n", "      <td>85496</td>\n", "      <td>0.371</td>\n", "      <td>463878</td>\n", "      <td>8137807.04</td>\n", "      <td>278040286</td>\n", "      <td>2204652</td>\n", "      <td>29.27</td>\n", "      <td>24149</td>\n", "      <td>1222</td>\n", "      <td>336.98</td>\n", "      <td>6659.42</td>\n", "      <td>0.0114</td>\n", "      <td>0.0679</td>\n", "      <td>3258</td>\n", "      <td>32718</td>\n", "      <td>2497.79</td>\n", "      <td>248.73</td>\n", "      <td>0.0079</td>\n", "      <td>0.011</td>\n", "      <td>0.0506</td>\n", "      <td>0.0327</td>\n", "      <td>92724</td>\n", "      <td>265868</td>\n", "      <td>552790</td>\n", "      <td>0.3745</td>\n", "      <td>3047444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>王雅涛</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 16:53:09</td>\n", "      <td>2025-02-25 16:53:17</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>44.0</td>\n", "      <td>40.0</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.038</td>\n", "      <td>0.019</td>\n", "      <td>0.019</td>\n", "      <td>2.07</td>\n", "      <td>1.46</td>\n", "      <td>0.61</td>\n", "      <td>2025-02-25 16:52:38</td>\n", "      <td>2025-02-25</td>\n", "      <td>16:52:38</td>\n", "      <td>854</td>\n", "      <td>32552</td>\n", "      <td>3650735</td>\n", "      <td>7326</td>\n", "      <td>28</td>\n", "      <td>122436.78</td>\n", "      <td>0.059835</td>\n", "      <td>1250299.85</td>\n", "      <td>38566029</td>\n", "      <td>320331</td>\n", "      <td>32.42</td>\n", "      <td>5648</td>\n", "      <td>240</td>\n", "      <td>221.37</td>\n", "      <td>5209.58</td>\n", "      <td>0.0109</td>\n", "      <td>0.0684</td>\n", "      <td>419</td>\n", "      <td>7320</td>\n", "      <td>2984.01</td>\n", "      <td>170.81</td>\n", "      <td>0.0083</td>\n", "      <td>0.0176</td>\n", "      <td>0.0425</td>\n", "      <td>0.0325</td>\n", "      <td>13572</td>\n", "      <td>40632</td>\n", "      <td>85496</td>\n", "      <td>0.371</td>\n", "      <td>463878</td>\n", "      <td>8137807.04</td>\n", "      <td>278040286</td>\n", "      <td>2204652</td>\n", "      <td>29.27</td>\n", "      <td>24149</td>\n", "      <td>1222</td>\n", "      <td>336.98</td>\n", "      <td>6659.42</td>\n", "      <td>0.0114</td>\n", "      <td>0.0679</td>\n", "      <td>3258</td>\n", "      <td>32718</td>\n", "      <td>2497.79</td>\n", "      <td>248.73</td>\n", "      <td>0.0079</td>\n", "      <td>0.011</td>\n", "      <td>0.0506</td>\n", "      <td>0.0327</td>\n", "      <td>92724</td>\n", "      <td>265868</td>\n", "      <td>552790</td>\n", "      <td>0.3745</td>\n", "      <td>3047444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>刘雨晴</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 17:14:07</td>\n", "      <td>2025-02-25 17:24:08</td>\n", "      <td>6</td>\n", "      <td>54.0</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "      <td>951.0</td>\n", "      <td>354.0</td>\n", "      <td>4.0</td>\n", "      <td>179851.0</td>\n", "      <td>76294.0</td>\n", "      <td>503.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>5965.37</td>\n", "      <td>2470.53</td>\n", "      <td>10.39</td>\n", "      <td>2025-02-25 17:12:34</td>\n", "      <td>2025-02-25</td>\n", "      <td>17:12:34</td>\n", "      <td>947</td>\n", "      <td>34719</td>\n", "      <td>4001943</td>\n", "      <td>7326</td>\n", "      <td>28</td>\n", "      <td>133930.37</td>\n", "      <td>0.054700</td>\n", "      <td>1250299.85</td>\n", "      <td>38566029</td>\n", "      <td>320331</td>\n", "      <td>32.42</td>\n", "      <td>5648</td>\n", "      <td>240</td>\n", "      <td>221.37</td>\n", "      <td>5209.58</td>\n", "      <td>0.0109</td>\n", "      <td>0.0684</td>\n", "      <td>419</td>\n", "      <td>7320</td>\n", "      <td>2984.01</td>\n", "      <td>170.81</td>\n", "      <td>0.0083</td>\n", "      <td>0.0176</td>\n", "      <td>0.0425</td>\n", "      <td>0.0325</td>\n", "      <td>13572</td>\n", "      <td>40632</td>\n", "      <td>85496</td>\n", "      <td>0.371</td>\n", "      <td>463878</td>\n", "      <td>8137807.04</td>\n", "      <td>278040286</td>\n", "      <td>2204652</td>\n", "      <td>29.27</td>\n", "      <td>24149</td>\n", "      <td>1222</td>\n", "      <td>336.98</td>\n", "      <td>6659.42</td>\n", "      <td>0.0114</td>\n", "      <td>0.0679</td>\n", "      <td>3258</td>\n", "      <td>32718</td>\n", "      <td>2497.79</td>\n", "      <td>248.73</td>\n", "      <td>0.0079</td>\n", "      <td>0.011</td>\n", "      <td>0.0506</td>\n", "      <td>0.0327</td>\n", "      <td>92724</td>\n", "      <td>265868</td>\n", "      <td>552790</td>\n", "      <td>0.3745</td>\n", "      <td>3047444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>刘雨晴</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 22:31:26</td>\n", "      <td>2025-02-25 22:31:26</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>186.0</td>\n", "      <td>186.0</td>\n", "      <td>186.0</td>\n", "      <td>17447.0</td>\n", "      <td>17447.0</td>\n", "      <td>17447.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>529.96</td>\n", "      <td>529.96</td>\n", "      <td>529.96</td>\n", "      <td>2025-02-25 22:13:23</td>\n", "      <td>2025-02-25</td>\n", "      <td>22:13:23</td>\n", "      <td>1538</td>\n", "      <td>48841</td>\n", "      <td>6106294</td>\n", "      <td>8616</td>\n", "      <td>47</td>\n", "      <td>206712.59</td>\n", "      <td>0.041681</td>\n", "      <td>1250299.85</td>\n", "      <td>38566029</td>\n", "      <td>320331</td>\n", "      <td>32.42</td>\n", "      <td>5648</td>\n", "      <td>240</td>\n", "      <td>221.37</td>\n", "      <td>5209.58</td>\n", "      <td>0.0109</td>\n", "      <td>0.0684</td>\n", "      <td>419</td>\n", "      <td>7320</td>\n", "      <td>2984.01</td>\n", "      <td>170.81</td>\n", "      <td>0.0083</td>\n", "      <td>0.0176</td>\n", "      <td>0.0425</td>\n", "      <td>0.0325</td>\n", "      <td>13572</td>\n", "      <td>40632</td>\n", "      <td>85496</td>\n", "      <td>0.371</td>\n", "      <td>463878</td>\n", "      <td>8137807.04</td>\n", "      <td>278040286</td>\n", "      <td>2204652</td>\n", "      <td>29.27</td>\n", "      <td>24149</td>\n", "      <td>1222</td>\n", "      <td>336.98</td>\n", "      <td>6659.42</td>\n", "      <td>0.0114</td>\n", "      <td>0.0679</td>\n", "      <td>3258</td>\n", "      <td>32718</td>\n", "      <td>2497.79</td>\n", "      <td>248.73</td>\n", "      <td>0.0079</td>\n", "      <td>0.011</td>\n", "      <td>0.0506</td>\n", "      <td>0.0327</td>\n", "      <td>92724</td>\n", "      <td>265868</td>\n", "      <td>552790</td>\n", "      <td>0.3745</td>\n", "      <td>3047444</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  optimizer_name macro_action_first     start_timestamp       end_timestamp  \\\n", "0            刘雨晴               暂停广告 2025-02-25 14:11:31 2025-02-25 14:12:27   \n", "1            刘雨晴               暂停广告 2025-02-25 15:23:48 2025-02-25 15:24:38   \n", "2            王雅涛               暂停广告 2025-02-25 16:53:09 2025-02-25 16:53:17   \n", "3            刘雨晴               暂停广告 2025-02-25 17:14:07 2025-02-25 17:24:08   \n", "4            刘雨晴               暂停广告 2025-02-25 22:31:26 2025-02-25 22:31:26   \n", "\n", "   timestamp_count  ad_activation_sum  ad_activation_max  ad_activation_min  \\\n", "0                3                2.0                1.0                0.0   \n", "1                2                1.0                1.0                0.0   \n", "2                2                0.0                0.0                0.0   \n", "3                6               54.0               25.0                0.0   \n", "4                1                1.0                1.0                1.0   \n", "\n", "   ad_c_sum  ad_c_max  ad_c_min  ad_i_sum  ad_i_max  ad_i_min  ad_pay1_sum  \\\n", "0     230.0     176.0       3.0   28518.0   21386.0     782.0          0.0   \n", "1     176.0     176.0       0.0   21496.0   21429.0      67.0          0.0   \n", "2       0.0       0.0       0.0      44.0      40.0       4.0          0.0   \n", "3     951.0     354.0       4.0  179851.0   76294.0     503.0          0.0   \n", "4     186.0     186.0     186.0   17447.0   17447.0   17447.0          0.0   \n", "\n", "   ad_pay1_max  ad_pay1_min  ad_pay_user_sum  ad_pay_user_max  \\\n", "0          0.0          0.0              0.0              0.0   \n", "1          0.0          0.0              0.0              0.0   \n", "2          0.0          0.0              0.0              0.0   \n", "3          0.0          0.0              0.0              0.0   \n", "4          0.0          0.0              0.0              0.0   \n", "\n", "   ad_pay_user_min  ad_roi1_sum  ad_roi1_max  ad_roi1_min  ad_roi_bid_sum  \\\n", "0              0.0          0.0          0.0          0.0           0.000   \n", "1              0.0          0.0          0.0          0.0           0.000   \n", "2              0.0          0.0          0.0          0.0           0.038   \n", "3              0.0          0.0          0.0          0.0           0.000   \n", "4              0.0          0.0          0.0          0.0           0.000   \n", "\n", "   ad_roi_bid_max  ad_roi_bid_min  ad_s_sum  ad_s_max  ad_s_min  \\\n", "0           0.000           0.000    936.61    552.89     33.41   \n", "1           0.000           0.000    554.19    553.08      1.11   \n", "2           0.019           0.019      2.07      1.46      0.61   \n", "3           0.000           0.000   5965.37   2470.53     10.39   \n", "4           0.000           0.000    529.96    529.96    529.96   \n", "\n", "       snap_timestamp    snap_day snap_time  total_activation  total_c  \\\n", "0 2025-02-25 13:52:09  2025-02-25  13:52:09               546    26050   \n", "1 2025-02-25 15:13:12  2025-02-25  15:13:12               651    29181   \n", "2 2025-02-25 16:52:38  2025-02-25  16:52:38               854    32552   \n", "3 2025-02-25 17:12:34  2025-02-25  17:12:34               947    34719   \n", "4 2025-02-25 22:13:23  2025-02-25  22:13:23              1538    48841   \n", "\n", "   total_i  total_pay1  total_pay_user    total_s  total_roi1  cost_studio7  \\\n", "0  2888030         764              21   94344.48    0.008098    1250299.85   \n", "1  3223321        4658              22  107582.40    0.043297    1250299.85   \n", "2  3650735        7326              28  122436.78    0.059835    1250299.85   \n", "3  4001943        7326              28  133930.37    0.054700    1250299.85   \n", "4  6106294        8616              47  206712.59    0.041681    1250299.85   \n", "\n", "   show_studio7  click_studio7  cost_by_thousand_show_studio7  \\\n", "0      38566029         320331                          32.42   \n", "1      38566029         320331                          32.42   \n", "2      38566029         320331                          32.42   \n", "3      38566029         320331                          32.42   \n", "4      38566029         320331                          32.42   \n", "\n", "   new_user_studio7  new_paid_user_studio7  new_user_cost_studio7  \\\n", "0              5648                    240                 221.37   \n", "1              5648                    240                 221.37   \n", "2              5648                    240                 221.37   \n", "3              5648                    240                 221.37   \n", "4              5648                    240                 221.37   \n", "\n", "   new_paid_user_cost_studio7  roi1_studio7  roi7_studio7  convert_studio7  \\\n", "0                     5209.58        0.0109        0.0684              419   \n", "1                     5209.58        0.0109        0.0684              419   \n", "2                     5209.58        0.0109        0.0684              419   \n", "3                     5209.58        0.0109        0.0684              419   \n", "4                     5209.58        0.0109        0.0684              419   \n", "\n", "   active_studio7  convert_cost_studio7  active_cost_studio7  ctr_studio7  \\\n", "0            7320               2984.01               170.81       0.0083   \n", "1            7320               2984.01               170.81       0.0083   \n", "2            7320               2984.01               170.81       0.0083   \n", "3            7320               2984.01               170.81       0.0083   \n", "4            7320               2984.01               170.81       0.0083   \n", "\n", "   cvr_studio7  new_paid_rate_studio7  roi3_studio7  pay1_studio7  \\\n", "0       0.0176                 0.0425        0.0325         13572   \n", "1       0.0176                 0.0425        0.0325         13572   \n", "2       0.0176                 0.0425        0.0325         13572   \n", "3       0.0176                 0.0425        0.0325         13572   \n", "4       0.0176                 0.0425        0.0325         13572   \n", "\n", "   pay3_studio7  pay7_studio7  total_roi_studio7  total_pay_studio7  \\\n", "0         40632         85496              0.371             463878   \n", "1         40632         85496              0.371             463878   \n", "2         40632         85496              0.371             463878   \n", "3         40632         85496              0.371             463878   \n", "4         40632         85496              0.371             463878   \n", "\n", "   cost_studio30  show_studio30  click_studio30  \\\n", "0     8137807.04      278040286         2204652   \n", "1     8137807.04      278040286         2204652   \n", "2     8137807.04      278040286         2204652   \n", "3     8137807.04      278040286         2204652   \n", "4     8137807.04      278040286         2204652   \n", "\n", "   cost_by_thousand_show_studio30  new_user_studio30  new_paid_user_studio30  \\\n", "0                           29.27              24149                    1222   \n", "1                           29.27              24149                    1222   \n", "2                           29.27              24149                    1222   \n", "3                           29.27              24149                    1222   \n", "4                           29.27              24149                    1222   \n", "\n", "   new_user_cost_studio30  new_paid_user_cost_studio30  roi1_studio30  \\\n", "0                  336.98                      6659.42         0.0114   \n", "1                  336.98                      6659.42         0.0114   \n", "2                  336.98                      6659.42         0.0114   \n", "3                  336.98                      6659.42         0.0114   \n", "4                  336.98                      6659.42         0.0114   \n", "\n", "   roi7_studio30  convert_studio30  active_studio30  convert_cost_studio30  \\\n", "0         0.0679              3258            32718                2497.79   \n", "1         0.0679              3258            32718                2497.79   \n", "2         0.0679              3258            32718                2497.79   \n", "3         0.0679              3258            32718                2497.79   \n", "4         0.0679              3258            32718                2497.79   \n", "\n", "   active_cost_studio30  ctr_studio30  cvr_studio30  new_paid_rate_studio30  \\\n", "0                248.73        0.0079         0.011                  0.0506   \n", "1                248.73        0.0079         0.011                  0.0506   \n", "2                248.73        0.0079         0.011                  0.0506   \n", "3                248.73        0.0079         0.011                  0.0506   \n", "4                248.73        0.0079         0.011                  0.0506   \n", "\n", "   roi3_studio30  pay1_studio30  pay3_studio30  pay7_studio30  \\\n", "0         0.0327          92724         265868         552790   \n", "1         0.0327          92724         265868         552790   \n", "2         0.0327          92724         265868         552790   \n", "3         0.0327          92724         265868         552790   \n", "4         0.0327          92724         265868         552790   \n", "\n", "   total_roi_studio30  total_pay_studio30  \n", "0              0.3745             3047444  \n", "1              0.3745             3047444  \n", "2              0.3745             3047444  \n", "3              0.3745             3047444  \n", "4              0.3745             3047444  "]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["market_snapshot_df_complete.pop('snap_timestamp')\n", "market_snapshot_df_complete['timestamp'] = pd.to_datetime(market_snapshot_df_complete['snap_day'].astype(str) + ' ' + market_snapshot_df_complete['snap_time'].astype(str))\n", "time_column = market_snapshot_df_complete.pop('timestamp')\n", "market_snapshot_df_complete.insert(0, 'snap_timestamp', time_column)\n", "\n", "data = pd.merge_asof(\n", "    stop_action_result.sort_values('timestamp_first'),\n", "    market_snapshot_df_complete.sort_values('snap_timestamp'),\n", "    left_on='timestamp_first',\n", "    right_on='snap_timestamp',\n", "    direction='backward'\n", ").rename(columns={\n", "    'timestamp_first': 'start_timestamp',\n", "    'timestamp_last': 'end_timestamp',\n", "})\n", "data.head()"]}, {"cell_type": "markdown", "id": "18c26b09", "metadata": {}, "source": ["## 从广告维度分析执行动作与否和特征分布之间的关系"]}, {"cell_type": "code", "execution_count": null, "id": "75bf0a47", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3316/3324686370.py:5: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_stop['macro_action'] = '暂停广告'\n", "/tmp/ipykernel_3316/3324686370.py:6: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  operation_df_adv_start['macro_action'] = '重启广告'\n"]}], "source": ["operation_df_adv = operation_df[(operation_df['operation_target'] == '广告') & (~operation_df['operation_log'].isna())]\n", "\n", "operation_df_adv_stop = operation_df_adv[operation_df_adv['operation_log'] == \"[修改 操作状态: 正常 -> 暂停]\"]\n", "operation_df_adv_start = operation_df_adv[operation_df_adv['operation_log'] == \"[修改 操作状态: 暂停 -> 正常]\"]\n", "operation_df_adv_stop['macro_action'] = '暂停广告'\n", "operation_df_adv_start['macro_action'] = '重启广告'\n", "operation_df_adv_stop.shape[0], operation_df_adv_start.shape[0]"]}, {"cell_type": "markdown", "id": "2fc48d5e", "metadata": {}, "source": ["### 关闭广告"]}, {"cell_type": "code", "execution_count": null, "id": "37d95d93", "metadata": {}, "outputs": [], "source": ["# 合并优化师动作和广告召回数据\n", "stop_actions = operation_df_adv_stop.copy()\n", "stop_actions['timestamp'] = pd.to_datetime(stop_actions['operation_day'].astype(str) + ' ' + stop_actions['operation_time'].astype(str))\n", "adv_df['timestamp'] = pd.to_datetime(adv_df['snap_day'].astype(str) + ' ' + adv_df['snap_time'].astype(str))\n", "\n", "adv_df = adv_df.sort_values('timestamp')\n", "stop_actions = stop_actions.sort_values('timestamp')\n", "\n", "stop_actions_background = pd.merge_asof(\n", "    stop_actions,\n", "    adv_df,\n", "    left_on='timestamp',\n", "    right_on='timestamp',\n", "    left_by='operation_object_id',\n", "    right_by='ad_id',\n", "    direction='backward'\n", ")\n", "\n", "# 这里需要注意不能用很久以前的快照数据作为当前操作的背景，因此需要筛选掉一批当天没有快照数据的广告\n", "stop_actions_background = stop_actions_background[stop_actions_background['snap_day'].astype(str) == stop_actions_background['operation_day'].astype(str)]"]}, {"cell_type": "code", "execution_count": 4, "id": "f0fc2a27", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["关停时的广告状态包括： ['投放中' '项目超出预算' '项目接近预算' '已暂停' '账户余额不足' '账号接近预算' '已被项目暂停' '广告接近预算'\n", " '账号超出预算' '不在投放时段']\n", "消耗不为0的关停广告数量为:  4401\n", "其中到达预算被关停的广告数量为： 50\n", "正常投放中，被关停的广告数量为： 3756\n"]}], "source": ["print('关停时的广告状态包括：', stop_actions_background['ad_status'].unique())\n", "print('消耗不为0的关停广告数量为: ', stop_actions_background.shape[0])\n", "print('其中到达预算被关停的广告数量为：', stop_actions_background[stop_actions_background['ad_status'] == '项目超出预算'].shape[0])\n", "print('正常投放中，被关停的广告数量为：', stop_actions_background[stop_actions_background['ad_status'] == '投放中'].shape[0])"]}, {"cell_type": "code", "execution_count": 5, "id": "5b9c0b8f", "metadata": {}, "outputs": [], "source": ["# 合并宏动作\n", "stop_actions_sorted = stop_actions_background.sort_values(['optimizer_name', 'timestamp'])\n", "stop_actions_sorted['time_diff'] = stop_actions_sorted.groupby('optimizer_name')['timestamp'].diff().dt.total_seconds()\n", "optimizer_change = (stop_actions_sorted['optimizer_name'] != stop_actions_sorted['optimizer_name'].shift()).astype(int)\n", "stop_actions_sorted['action_group'] =  ((stop_actions_sorted['time_diff'] > 600) | (stop_actions_sorted['optimizer_name'] != stop_actions_sorted['optimizer_name'].shift())).astype(int).cumsum()\n", "stop_actions_sorted['macro_timestamp'] = stop_actions_sorted.groupby('action_group')['timestamp'].transform('first')\n", "# stop_actions_sorted = stop_actions_sorted.drop(['time_diff', 'action_group', 'timestamp'], axis=1)\n", "stop_actions_sorted = stop_actions_sorted.drop(['time_diff', 'timestamp'], axis=1)"]}, {"cell_type": "code", "execution_count": 6, "id": "1a8b3a32", "metadata": {}, "outputs": [], "source": ["# 添加大盘数据\n", "market_snapshot_df_complete['snap_timestamp'] = pd.to_datetime(market_snapshot_df_complete['snap_day'].astype(str) + ' ' + market_snapshot_df_complete['snap_time'].astype(str))\n", "\n", "stop_actions_sorted = stop_actions_sorted.sort_values('macro_timestamp')\n", "market_snapshot_df_complete = market_snapshot_df_complete.sort_values('snap_timestamp')\n", "\n", "data_stop_actions = pd.merge_asof(\n", "    stop_actions_sorted,\n", "    market_snapshot_df_complete.drop(['snap_day', 'snap_time'], axis=1),\n", "    left_on='macro_timestamp',\n", "    right_on='snap_timestamp',\n", "    direction='backward'\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "899d009c", "metadata": {}, "outputs": [], "source": ["stop_timestamps = data_stop_actions['snap_timestamp'].unique()\n", "adv_market_df = pd.merge_asof(\n", "    adv_df.sort_values('timestamp'),\n", "    market_snapshot_df_complete.sort_values('snap_timestamp').drop(['snap_day', 'snap_time'], axis=1),\n", "    left_on='timestamp',\n", "    right_on='snap_timestamp'\n", ")"]}, {"cell_type": "code", "execution_count": 58, "id": "8ec3d789", "metadata": {}, "outputs": [], "source": ["data_stop_actions['match_key'] = data_stop_actions['snap_timestamp'].astype(str) + '_' + data_stop_actions['operation_object_id'].astype(str)\n", "actioned_adv_market_df = adv_market_df[adv_market_df['timestamp'].isin(data_stop_actions['snap_timestamp'])].copy()\n", "actioned_adv_market_df['match_key'] = actioned_adv_market_df['timestamp'].astype(str) + '_' + actioned_adv_market_df['ad_id'].astype(str)\n", "actioned_adv_market_df['label'] = actioned_adv_market_df['match_key'].isin(data_stop_actions['match_key']).astype(int)\n", "actioned_adv_market_df = actioned_adv_market_df[~((actioned_adv_market_df['label'] == 0) & (actioned_adv_market_df['ad_status'] == '已暂停'))]"]}, {"cell_type": "code", "execution_count": 91, "id": "81cde2f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["ad_status\n", "投放中       3756\n", "账号超出预算     302\n", "已被项目暂停      80\n", "不在投放时段      76\n", "广告接近预算      51\n", "项目超出预算      50\n", "账号接近预算      41\n", "账户余额不足      33\n", "项目接近预算      10\n", "已暂停          2\n", "Name: count, dtype: int64"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["data_stop_actions['ad_status'].value_counts()"]}, {"cell_type": "code", "execution_count": 50, "id": "fe5cb9f3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3316/1491792468.py:1: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  actioned_adv_market_df[adv_market_df['label'] == 1].head(2)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>snap_day</th>\n", "      <th>snap_time</th>\n", "      <th>platform_id</th>\n", "      <th>channel_id</th>\n", "      <th>ad_id</th>\n", "      <th>ad_activation</th>\n", "      <th>ad_bid</th>\n", "      <th>ad_budget</th>\n", "      <th>ad_c</th>\n", "      <th>ad_i</th>\n", "      <th>ad_pay1</th>\n", "      <th>ad_pay_user</th>\n", "      <th>ad_roi1</th>\n", "      <th>ad_roi_bid</th>\n", "      <th>ad_s</th>\n", "      <th>ad_status</th>\n", "      <th>cost_adv7</th>\n", "      <th>show_adv7</th>\n", "      <th>click_adv7</th>\n", "      <th>cost_by_thousand_show_adv7</th>\n", "      <th>new_user_adv7</th>\n", "      <th>new_paid_user_adv7</th>\n", "      <th>new_user_cost_adv7</th>\n", "      <th>new_paid_user_cost_adv7</th>\n", "      <th>roi1_adv7</th>\n", "      <th>roi7_adv7</th>\n", "      <th>convert_adv7</th>\n", "      <th>active_adv7</th>\n", "      <th>convert_cost_adv7</th>\n", "      <th>active_cost_adv7</th>\n", "      <th>ctr_adv7</th>\n", "      <th>cvr_adv7</th>\n", "      <th>new_paid_rate_adv7</th>\n", "      <th>roi3_adv7</th>\n", "      <th>pay1_adv7</th>\n", "      <th>pay3_adv7</th>\n", "      <th>pay7_adv7</th>\n", "      <th>total_roi_adv7</th>\n", "      <th>total_pay_adv7</th>\n", "      <th>timestamp</th>\n", "      <th>total_activation</th>\n", "      <th>total_c</th>\n", "      <th>total_i</th>\n", "      <th>total_pay1</th>\n", "      <th>total_pay_user</th>\n", "      <th>total_s</th>\n", "      <th>total_roi1</th>\n", "      <th>cost_studio7</th>\n", "      <th>show_studio7</th>\n", "      <th>click_studio7</th>\n", "      <th>cost_by_thousand_show_studio7</th>\n", "      <th>new_user_studio7</th>\n", "      <th>new_paid_user_studio7</th>\n", "      <th>new_user_cost_studio7</th>\n", "      <th>new_paid_user_cost_studio7</th>\n", "      <th>roi1_studio7</th>\n", "      <th>roi7_studio7</th>\n", "      <th>convert_studio7</th>\n", "      <th>active_studio7</th>\n", "      <th>convert_cost_studio7</th>\n", "      <th>active_cost_studio7</th>\n", "      <th>ctr_studio7</th>\n", "      <th>cvr_studio7</th>\n", "      <th>new_paid_rate_studio7</th>\n", "      <th>roi3_studio7</th>\n", "      <th>pay1_studio7</th>\n", "      <th>pay3_studio7</th>\n", "      <th>pay7_studio7</th>\n", "      <th>total_roi_studio7</th>\n", "      <th>total_pay_studio7</th>\n", "      <th>cost_studio30</th>\n", "      <th>show_studio30</th>\n", "      <th>click_studio30</th>\n", "      <th>cost_by_thousand_show_studio30</th>\n", "      <th>new_user_studio30</th>\n", "      <th>new_paid_user_studio30</th>\n", "      <th>new_user_cost_studio30</th>\n", "      <th>new_paid_user_cost_studio30</th>\n", "      <th>roi1_studio30</th>\n", "      <th>roi7_studio30</th>\n", "      <th>convert_studio30</th>\n", "      <th>active_studio30</th>\n", "      <th>convert_cost_studio30</th>\n", "      <th>active_cost_studio30</th>\n", "      <th>ctr_studio30</th>\n", "      <th>cvr_studio30</th>\n", "      <th>new_paid_rate_studio30</th>\n", "      <th>roi3_studio30</th>\n", "      <th>pay1_studio30</th>\n", "      <th>pay3_studio30</th>\n", "      <th>pay7_studio30</th>\n", "      <th>total_roi_studio30</th>\n", "      <th>total_pay_studio30</th>\n", "      <th>snap_timestamp</th>\n", "      <th>match_key</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11191</th>\n", "      <td>2025-02-25</td>\n", "      <td>13:52:09</td>\n", "      <td>1</td>\n", "      <td>trade</td>\n", "      <td>7474823075675684901</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "      <td>782</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>33.41</td>\n", "      <td>项目超出预算</td>\n", "      <td>2311.12</td>\n", "      <td>74480.0</td>\n", "      <td>300.0</td>\n", "      <td>31.03</td>\n", "      <td>34.0</td>\n", "      <td>1.0</td>\n", "      <td>67.97</td>\n", "      <td>2311.12</td>\n", "      <td>0.0026</td>\n", "      <td>0.0026</td>\n", "      <td>1.0</td>\n", "      <td>41.0</td>\n", "      <td>2311.12</td>\n", "      <td>56.37</td>\n", "      <td>0.0040</td>\n", "      <td>0.1133</td>\n", "      <td>0.0294</td>\n", "      <td>0.0026</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>0.0026</td>\n", "      <td>6.0</td>\n", "      <td>2025-02-25 13:52:09</td>\n", "      <td>546</td>\n", "      <td>26050</td>\n", "      <td>2888030</td>\n", "      <td>764</td>\n", "      <td>21</td>\n", "      <td>94344.48</td>\n", "      <td>0.008098</td>\n", "      <td>1250299.85</td>\n", "      <td>38566029</td>\n", "      <td>320331</td>\n", "      <td>32.42</td>\n", "      <td>5648</td>\n", "      <td>240</td>\n", "      <td>221.37</td>\n", "      <td>5209.58</td>\n", "      <td>0.0109</td>\n", "      <td>0.0684</td>\n", "      <td>419</td>\n", "      <td>7320</td>\n", "      <td>2984.01</td>\n", "      <td>170.81</td>\n", "      <td>0.0083</td>\n", "      <td>0.0176</td>\n", "      <td>0.0425</td>\n", "      <td>0.0325</td>\n", "      <td>13572</td>\n", "      <td>40632</td>\n", "      <td>85496</td>\n", "      <td>0.371</td>\n", "      <td>463878</td>\n", "      <td>8137807.04</td>\n", "      <td>278040286</td>\n", "      <td>2204652</td>\n", "      <td>29.27</td>\n", "      <td>24149</td>\n", "      <td>1222</td>\n", "      <td>336.98</td>\n", "      <td>6659.42</td>\n", "      <td>0.0114</td>\n", "      <td>0.0679</td>\n", "      <td>3258</td>\n", "      <td>32718</td>\n", "      <td>2497.79</td>\n", "      <td>248.73</td>\n", "      <td>0.0079</td>\n", "      <td>0.011</td>\n", "      <td>0.0506</td>\n", "      <td>0.0327</td>\n", "      <td>92724</td>\n", "      <td>265868</td>\n", "      <td>552790</td>\n", "      <td>0.3745</td>\n", "      <td>3047444</td>\n", "      <td>2025-02-25 13:52:09</td>\n", "      <td>2025-02-25 13:52:09_7474823075675684901</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12539</th>\n", "      <td>2025-02-25</td>\n", "      <td>13:52:09</td>\n", "      <td>1</td>\n", "      <td>trade</td>\n", "      <td>7474885372528820250</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>176</td>\n", "      <td>21386</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>552.89</td>\n", "      <td>投放中</td>\n", "      <td>1029.57</td>\n", "      <td>38190.0</td>\n", "      <td>322.0</td>\n", "      <td>26.96</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1029.57</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>1029.57</td>\n", "      <td>0.0084</td>\n", "      <td>0.0031</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>2025-02-25 13:52:09</td>\n", "      <td>546</td>\n", "      <td>26050</td>\n", "      <td>2888030</td>\n", "      <td>764</td>\n", "      <td>21</td>\n", "      <td>94344.48</td>\n", "      <td>0.008098</td>\n", "      <td>1250299.85</td>\n", "      <td>38566029</td>\n", "      <td>320331</td>\n", "      <td>32.42</td>\n", "      <td>5648</td>\n", "      <td>240</td>\n", "      <td>221.37</td>\n", "      <td>5209.58</td>\n", "      <td>0.0109</td>\n", "      <td>0.0684</td>\n", "      <td>419</td>\n", "      <td>7320</td>\n", "      <td>2984.01</td>\n", "      <td>170.81</td>\n", "      <td>0.0083</td>\n", "      <td>0.0176</td>\n", "      <td>0.0425</td>\n", "      <td>0.0325</td>\n", "      <td>13572</td>\n", "      <td>40632</td>\n", "      <td>85496</td>\n", "      <td>0.371</td>\n", "      <td>463878</td>\n", "      <td>8137807.04</td>\n", "      <td>278040286</td>\n", "      <td>2204652</td>\n", "      <td>29.27</td>\n", "      <td>24149</td>\n", "      <td>1222</td>\n", "      <td>336.98</td>\n", "      <td>6659.42</td>\n", "      <td>0.0114</td>\n", "      <td>0.0679</td>\n", "      <td>3258</td>\n", "      <td>32718</td>\n", "      <td>2497.79</td>\n", "      <td>248.73</td>\n", "      <td>0.0079</td>\n", "      <td>0.011</td>\n", "      <td>0.0506</td>\n", "      <td>0.0327</td>\n", "      <td>92724</td>\n", "      <td>265868</td>\n", "      <td>552790</td>\n", "      <td>0.3745</td>\n", "      <td>3047444</td>\n", "      <td>2025-02-25 13:52:09</td>\n", "      <td>2025-02-25 13:52:09_7474885372528820250</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         snap_day snap_time  platform_id channel_id                ad_id  \\\n", "11191  2025-02-25  13:52:09            1      trade  7474823075675684901   \n", "12539  2025-02-25  13:52:09            1      trade  7474885372528820250   \n", "\n", "       ad_activation  ad_bid  ad_budget  ad_c   ad_i  ad_pay1  ad_pay_user  \\\n", "11191              0     0.0        0.0     3    782        0            0   \n", "12539              1     0.0        0.0   176  21386        0            0   \n", "\n", "       ad_roi1  ad_roi_bid    ad_s ad_status  cost_adv7  show_adv7  \\\n", "11191      0.0         0.0   33.41    项目超出预算    2311.12    74480.0   \n", "12539      0.0         0.0  552.89       投放中    1029.57    38190.0   \n", "\n", "       click_adv7  cost_by_thousand_show_adv7  new_user_adv7  \\\n", "11191       300.0                       31.03           34.0   \n", "12539       322.0                       26.96            1.0   \n", "\n", "       new_paid_user_adv7  new_user_cost_adv7  new_paid_user_cost_adv7  \\\n", "11191                 1.0               67.97                  2311.12   \n", "12539                 0.0             1029.57                     0.00   \n", "\n", "       roi1_adv7  roi7_adv7  convert_adv7  active_adv7  convert_cost_adv7  \\\n", "11191     0.0026     0.0026           1.0         41.0            2311.12   \n", "12539     0.0000     0.0000           0.0          1.0               0.00   \n", "\n", "       active_cost_adv7  ctr_adv7  cvr_adv7  new_paid_rate_adv7  roi3_adv7  \\\n", "11191             56.37    0.0040    0.1133              0.0294     0.0026   \n", "12539           1029.57    0.0084    0.0031              0.0000     0.0000   \n", "\n", "       pay1_adv7  pay3_adv7  pay7_adv7  total_roi_adv7  total_pay_adv7  \\\n", "11191        6.0        6.0        6.0          0.0026             6.0   \n", "12539        0.0        0.0        0.0          0.0000             0.0   \n", "\n", "                timestamp  total_activation  total_c  total_i  total_pay1  \\\n", "11191 2025-02-25 13:52:09               546    26050  2888030         764   \n", "12539 2025-02-25 13:52:09               546    26050  2888030         764   \n", "\n", "       total_pay_user   total_s  total_roi1  cost_studio7  show_studio7  \\\n", "11191              21  94344.48    0.008098    1250299.85      38566029   \n", "12539              21  94344.48    0.008098    1250299.85      38566029   \n", "\n", "       click_studio7  cost_by_thousand_show_studio7  new_user_studio7  \\\n", "11191         320331                          32.42              5648   \n", "12539         320331                          32.42              5648   \n", "\n", "       new_paid_user_studio7  new_user_cost_studio7  \\\n", "11191                    240                 221.37   \n", "12539                    240                 221.37   \n", "\n", "       new_paid_user_cost_studio7  roi1_studio7  roi7_studio7  \\\n", "11191                     5209.58        0.0109        0.0684   \n", "12539                     5209.58        0.0109        0.0684   \n", "\n", "       convert_studio7  active_studio7  convert_cost_studio7  \\\n", "11191              419            7320               2984.01   \n", "12539              419            7320               2984.01   \n", "\n", "       active_cost_studio7  ctr_studio7  cvr_studio7  new_paid_rate_studio7  \\\n", "11191               170.81       0.0083       0.0176                 0.0425   \n", "12539               170.81       0.0083       0.0176                 0.0425   \n", "\n", "       roi3_studio7  pay1_studio7  pay3_studio7  pay7_studio7  \\\n", "11191        0.0325         13572         40632         85496   \n", "12539        0.0325         13572         40632         85496   \n", "\n", "       total_roi_studio7  total_pay_studio7  cost_studio30  show_studio30  \\\n", "11191              0.371             463878     8137807.04      278040286   \n", "12539              0.371             463878     8137807.04      278040286   \n", "\n", "       click_studio30  cost_by_thousand_show_studio30  new_user_studio30  \\\n", "11191         2204652                           29.27              24149   \n", "12539         2204652                           29.27              24149   \n", "\n", "       new_paid_user_studio30  new_user_cost_studio30  \\\n", "11191                    1222                  336.98   \n", "12539                    1222                  336.98   \n", "\n", "       new_paid_user_cost_studio30  roi1_studio30  roi7_studio30  \\\n", "11191                      6659.42         0.0114         0.0679   \n", "12539                      6659.42         0.0114         0.0679   \n", "\n", "       convert_studio30  active_studio30  convert_cost_studio30  \\\n", "11191              3258            32718                2497.79   \n", "12539              3258            32718                2497.79   \n", "\n", "       active_cost_studio30  ctr_studio30  cvr_studio30  \\\n", "11191                248.73        0.0079         0.011   \n", "12539                248.73        0.0079         0.011   \n", "\n", "       new_paid_rate_studio30  roi3_studio30  pay1_studio30  pay3_studio30  \\\n", "11191                  0.0506         0.0327          92724         265868   \n", "12539                  0.0506         0.0327          92724         265868   \n", "\n", "       pay7_studio30  total_roi_studio30  total_pay_studio30  \\\n", "11191         552790              0.3745             3047444   \n", "12539         552790              0.3745             3047444   \n", "\n", "           snap_timestamp                                match_key  label  \n", "11191 2025-02-25 13:52:09  2025-02-25 13:52:09_7474823075675684901      1  \n", "12539 2025-02-25 13:52:09  2025-02-25 13:52:09_7474885372528820250      1  "]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["actioned_adv_market_df[adv_market_df['label'] == 1].head(2)"]}, {"cell_type": "code", "execution_count": 156, "id": "8760772d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["511331\n", "label=0时值为0的占比为99.09%\n", "label=1时值为0的占比为98.96%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_discrete_distributions(df, column_name, bins, pop_0=False):\n", "    df_0 = df[df['label'] == 0].copy()\n", "    df_1 = df[df['label'] == 1].copy()\n", "    \n", "    if pop_0:\n", "        print('label=0时值为0的占比为{}%'.format(round(df_0[df_0[column_name] == 0].shape[0] / df_0.shape[0] * 100, 2)))\n", "        print('label=1时值为0的占比为{}%'.format(round(df_1[df_1[column_name] == 0].shape[0] / df_1.shape[0] * 100, 2)))\n", "        df_0 = df_0[df_0[column_name] !=0]\n", "        df_1 = df_1[df_1[column_name] !=0]\n", "\n", "    df_0['bin'] = pd.cut(df_0[column_name], bins=bins)\n", "    df_1['bin'] = pd.cut(df_1[column_name], bins=bins)\n", "\n", "    counts_0 = (df_0['bin'].value_counts().sort_index() / len(df_0) * 100).round(2)\n", "    counts_1 = (df_1['bin'].value_counts().sort_index() / len(df_1) * 100).round(2)\n", "\n", "    # 创建单个图\n", "    plt.figure(figsize=(15, 6))\n", "    \n", "    # 获取x位置\n", "    x = np.arange(len(counts_0))\n", "    width = 0.35  # 柱子的宽度\n", "    \n", "    # 绘制两组柱状图\n", "    bar1 = plt.bar(x - width/2, counts_0, width, label='Label 0', color='skyblue', edgecolor='black')\n", "    bar2 = plt.bar(x + width/2, counts_1, width, label='Label 1', color='lightcoral', edgecolor='black')\n", "\n", "    plt.title(f'Distribution for {column_name} (%)')\n", "    plt.xlabel('Bins')\n", "    plt.ylabel('Percentage (%)')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 设置x轴刻度\n", "    plt.xticks(x, counts_0.index, rotation=45)\n", "    \n", "    # 添加图例\n", "    plt.legend()\n", "\n", "    # 在柱子上添加标签\n", "    def add_value_labels(bars):\n", "        for bar in bars:\n", "            height = bar.get_height()\n", "            plt.text(bar.get_x() + bar.get_width()/2., height,\n", "                    f'{height:.1f}%',\n", "                    ha='center', va='bottom')\n", "\n", "    add_value_labels(bar1)\n", "    add_value_labels(bar2)\n", "\n", "    # 调整y轴范围\n", "    plt.ylim(0, max(counts_0.max(), counts_1.max()) * 1.1)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "column_bins = {\n", "    # 'ad_c': ([-1,0,2,5,10,20,50,100,200,500,1000,10000], False),\n", "    # 'ad_i': ([-1,0,2,5,10,20,50,100,200,500,1000,10000,100000,1000000], False),\n", "    # 'ad_pay1': ([0,6,12,30,68,128,328,648,1280], True),\n", "    # 'ad_pay_user': ([0,1,2,3,4,5,20], True),\n", "    # 'ad_roi1': ([0,0.001,0.002,0.004,0.007,0.01,0.02,0.04,0.07,0.1,0.2,0.4,0.7,1,2,10], True),\n", "    'ad_pay_s': ([0,1,100,500,1000,2000,3000,4000,5000,6000,8000,10000,50000], True),\n", "}\n", "\n", "print(actioned_adv_market_df.shape[0])\n", "for column, (bin, pop_0) in column_bins.items():\n", "    plot_discrete_distributions(actioned_adv_market_df, column, bin, pop_0)"]}, {"cell_type": "markdown", "id": "500d22ce", "metadata": {}, "source": ["### 开启广告"]}, {"cell_type": "code", "execution_count": null, "id": "1bdd61f3", "metadata": {}, "outputs": [], "source": ["# 合并优化师动作和广告召回数据\n", "start_actions = operation_df_adv_start.copy()\n", "start_actions['timestamp'] = pd.to_datetime(start_actions['operation_day'].astype(str) + ' ' + start_actions['operation_time'].astype(str))\n", "stop_actions = operation_df_adv_stop.copy()\n", "stop_actions['timestamp'] = pd.to_datetime(stop_actions['operation_day'].astype(str) + ' ' + stop_actions['operation_time'].astype(str))\n", "actions = pd.concat([start_actions, stop_actions], axis=0)\n", "\n", "adv_history_df = adv_history_df.rename(columns={\n", "    'ad_id_adv7': 'ad_id',\n", "    'date_adv7': 'date'\n", "})\n", "\n", "actions_background = pd.merge(\n", "    actions,\n", "    adv_history_df,\n", "    how='left',\n", "    left_on=['operation_day', 'operation_object_id'],\n", "    right_on=['date', 'ad_id']\n", ")\n", "# 筛选当天重新开的广告\n", "actions_background = actions_background[actions_background['ad_id'].notna()]\n", "actions_background['label'] = (actions_background['macro_action'] == '重启广告').astype(int)"]}, {"cell_type": "code", "execution_count": 268, "id": "ea38e1e8", "metadata": {}, "outputs": [], "source": ["actions_background.to_csv('./start_actions_background.csv')"]}, {"cell_type": "code", "execution_count": 260, "id": "f97820af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>operation_day</th>\n", "      <th>operation_time</th>\n", "      <th>advertiser_studio_id</th>\n", "      <th>advertiser_id</th>\n", "      <th>optimizer_name</th>\n", "      <th>channel_operator</th>\n", "      <th>operation_object_id</th>\n", "      <th>operation_object_name</th>\n", "      <th>operation_target</th>\n", "      <th>operation_type</th>\n", "      <th>operation_log</th>\n", "      <th>sub_job_id</th>\n", "      <th>execute_date</th>\n", "      <th>macro_action</th>\n", "      <th>timestamp</th>\n", "      <th>ad_id</th>\n", "      <th>date</th>\n", "      <th>cost_adv7</th>\n", "      <th>show_adv7</th>\n", "      <th>click_adv7</th>\n", "      <th>cost_by_thousand_show_adv7</th>\n", "      <th>new_user_adv7</th>\n", "      <th>new_paid_user_adv7</th>\n", "      <th>new_user_cost_adv7</th>\n", "      <th>new_paid_user_cost_adv7</th>\n", "      <th>roi1_adv7</th>\n", "      <th>roi7_adv7</th>\n", "      <th>convert_adv7</th>\n", "      <th>active_adv7</th>\n", "      <th>convert_cost_adv7</th>\n", "      <th>active_cost_adv7</th>\n", "      <th>ctr_adv7</th>\n", "      <th>cvr_adv7</th>\n", "      <th>new_paid_rate_adv7</th>\n", "      <th>roi3_adv7</th>\n", "      <th>pay1_adv7</th>\n", "      <th>pay3_adv7</th>\n", "      <th>pay7_adv7</th>\n", "      <th>total_roi_adv7</th>\n", "      <th>total_pay_adv7</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-04-21</td>\n", "      <td>09:43:21</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1810972499865833</td>\n", "      <td>王雅涛</td>\n", "      <td><EMAIL></td>\n", "      <td>7490770323060293669</td>\n", "      <td>UBA-高系数-每付7R-0408-10514626</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-04-21 09:43:21</td>\n", "      <td>7.490770e+18</td>\n", "      <td>2025-04-21</td>\n", "      <td>12661.20</td>\n", "      <td>945498.0</td>\n", "      <td>6371.0</td>\n", "      <td>13.39</td>\n", "      <td>45.0</td>\n", "      <td>4.0</td>\n", "      <td>281.36</td>\n", "      <td>3165.30</td>\n", "      <td>0.0618</td>\n", "      <td>0.1430</td>\n", "      <td>7.0</td>\n", "      <td>45.0</td>\n", "      <td>1808.74</td>\n", "      <td>281.36</td>\n", "      <td>0.0067</td>\n", "      <td>0.0071</td>\n", "      <td>0.0889</td>\n", "      <td>0.0627</td>\n", "      <td>782.0</td>\n", "      <td>794.0</td>\n", "      <td>1810.0</td>\n", "      <td>0.2771</td>\n", "      <td>3508.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-04-07</td>\n", "      <td>11:54:18</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1690576895083527</td>\n", "      <td>王雅涛</td>\n", "      <td><EMAIL></td>\n", "      <td>7486067251000786985</td>\n", "      <td>站内mcff-0326-50-184252</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-04-07 11:54:18</td>\n", "      <td>7.486067e+18</td>\n", "      <td>2025-04-07</td>\n", "      <td>3078.92</td>\n", "      <td>182740.0</td>\n", "      <td>1097.0</td>\n", "      <td>16.85</td>\n", "      <td>21.0</td>\n", "      <td>3.0</td>\n", "      <td>146.62</td>\n", "      <td>1026.31</td>\n", "      <td>0.0331</td>\n", "      <td>0.0546</td>\n", "      <td>5.0</td>\n", "      <td>21.0</td>\n", "      <td>615.78</td>\n", "      <td>146.62</td>\n", "      <td>0.0060</td>\n", "      <td>0.0191</td>\n", "      <td>0.1429</td>\n", "      <td>0.0351</td>\n", "      <td>102.0</td>\n", "      <td>108.0</td>\n", "      <td>168.0</td>\n", "      <td>0.0546</td>\n", "      <td>168.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-03-07</td>\n", "      <td>00:01:11</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1810972841495568</td>\n", "      <td>刘雨晴</td>\n", "      <td><EMAIL></td>\n", "      <td>7477465721300582437</td>\n", "      <td>0303-502-#250303_1晨汇_2代理_3君_4真人剧情_5正装_6多场_7无_9氛围感_b3D_c横_40包</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-03-07 00:01:11</td>\n", "      <td>7.477466e+18</td>\n", "      <td>2025-03-07</td>\n", "      <td>114.51</td>\n", "      <td>4307.0</td>\n", "      <td>13.0</td>\n", "      <td>26.59</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>114.51</td>\n", "      <td>0.0030</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-03-07</td>\n", "      <td>00:01:28</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1810972841495568</td>\n", "      <td>刘雨晴</td>\n", "      <td><EMAIL></td>\n", "      <td>7477465814183919627</td>\n", "      <td>0303-553-#250214_1自制_2良_3良_4常_5街机_7无_8自制BOSS_9街机聚宝盆_b3D_c竖_.mp440包</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-03-07 00:01:28</td>\n", "      <td>7.477466e+18</td>\n", "      <td>2025-03-07</td>\n", "      <td>2.86</td>\n", "      <td>12.0</td>\n", "      <td>1.0</td>\n", "      <td>238.33</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0833</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-03-07</td>\n", "      <td>00:08:19</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1810972841495568</td>\n", "      <td>刘雨晴</td>\n", "      <td><EMAIL></td>\n", "      <td>7477495425432862759</td>\n", "      <td>0303-430-#250125_1楠升_2代理_3璐_4真人口播_5性感_6室内_7无_9大哥_b3D_c横_40包</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-03-07 00:08:19</td>\n", "      <td>7.477495e+18</td>\n", "      <td>2025-03-07</td>\n", "      <td>0.97</td>\n", "      <td>35.0</td>\n", "      <td>1.0</td>\n", "      <td>27.71</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0286</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  operation_day operation_time advertiser_studio_id     advertiser_id  \\\n", "2    2025-04-21       09:43:21                灵眸工作室  1810972499865833   \n", "3    2025-04-07       11:54:18                灵眸工作室  1690576895083527   \n", "5    2025-03-07       00:01:11                灵眸工作室  1810972841495568   \n", "6    2025-03-07       00:01:28                灵眸工作室  1810972841495568   \n", "7    2025-03-07       00:08:19                灵眸工作室  1810972841495568   \n", "\n", "  optimizer_name     channel_operator  operation_object_id  \\\n", "2            王雅涛  <EMAIL>  7490770323060293669   \n", "3            王雅涛  <EMAIL>  7486067251000786985   \n", "5            刘雨晴  <EMAIL>  7477465721300582437   \n", "6            刘雨晴  <EMAIL>  7477465814183919627   \n", "7            刘雨晴  <EMAIL>  7477495425432862759   \n", "\n", "                                                operation_object_name  \\\n", "2                                          UBA-高系数-每付7R-0408-10514626   \n", "3                                               站内mcff-0326-50-184252   \n", "5        0303-502-#250303_1晨汇_2代理_3君_4真人剧情_5正装_6多场_7无_9氛围感_b3D_c横_40包   \n", "6  0303-553-#250214_1自制_2良_3良_4常_5街机_7无_8自制BOSS_9街机聚宝盆_b3D_c竖_.mp440包   \n", "7         0303-430-#250125_1楠升_2代理_3璐_4真人口播_5性感_6室内_7无_9大哥_b3D_c横_40包   \n", "\n", "  operation_target operation_type        operation_log  sub_job_id  \\\n", "2               广告             修改  [修改 操作状态: 暂停 -> 正常]        1573   \n", "3               广告             修改  [修改 操作状态: 暂停 -> 正常]        1573   \n", "5               广告             修改  [修改 操作状态: 暂停 -> 正常]        1573   \n", "6               广告             修改  [修改 操作状态: 暂停 -> 正常]        1573   \n", "7               广告             修改  [修改 操作状态: 暂停 -> 正常]        1573   \n", "\n", "  execute_date macro_action           timestamp         ad_id        date  \\\n", "2   2025-05-19         重启广告 2025-04-21 09:43:21  7.490770e+18  2025-04-21   \n", "3   2025-05-19         重启广告 2025-04-07 11:54:18  7.486067e+18  2025-04-07   \n", "5   2025-05-19         重启广告 2025-03-07 00:01:11  7.477466e+18  2025-03-07   \n", "6   2025-05-19         重启广告 2025-03-07 00:01:28  7.477466e+18  2025-03-07   \n", "7   2025-05-19         重启广告 2025-03-07 00:08:19  7.477495e+18  2025-03-07   \n", "\n", "   cost_adv7  show_adv7  click_adv7  cost_by_thousand_show_adv7  \\\n", "2   12661.20   945498.0      6371.0                       13.39   \n", "3    3078.92   182740.0      1097.0                       16.85   \n", "5     114.51     4307.0        13.0                       26.59   \n", "6       2.86       12.0         1.0                      238.33   \n", "7       0.97       35.0         1.0                       27.71   \n", "\n", "   new_user_adv7  new_paid_user_adv7  new_user_cost_adv7  \\\n", "2           45.0                 4.0              281.36   \n", "3           21.0                 3.0              146.62   \n", "5            0.0                 0.0                0.00   \n", "6            0.0                 0.0                0.00   \n", "7            0.0                 0.0                0.00   \n", "\n", "   new_paid_user_cost_adv7  roi1_adv7  roi7_adv7  convert_adv7  active_adv7  \\\n", "2                  3165.30     0.0618     0.1430           7.0         45.0   \n", "3                  1026.31     0.0331     0.0546           5.0         21.0   \n", "5                     0.00     0.0000     0.0000           0.0          1.0   \n", "6                     0.00     0.0000     0.0000           0.0          0.0   \n", "7                     0.00     0.0000     0.0000           0.0          0.0   \n", "\n", "   convert_cost_adv7  active_cost_adv7  ctr_adv7  cvr_adv7  \\\n", "2            1808.74            281.36    0.0067    0.0071   \n", "3             615.78            146.62    0.0060    0.0191   \n", "5               0.00            114.51    0.0030    0.0000   \n", "6               0.00              0.00    0.0833    0.0000   \n", "7               0.00              0.00    0.0286    0.0000   \n", "\n", "   new_paid_rate_adv7  roi3_adv7  pay1_adv7  pay3_adv7  pay7_adv7  \\\n", "2              0.0889     0.0627      782.0      794.0     1810.0   \n", "3              0.1429     0.0351      102.0      108.0      168.0   \n", "5              0.0000     0.0000        0.0        0.0        0.0   \n", "6              0.0000     0.0000        0.0        0.0        0.0   \n", "7              0.0000     0.0000        0.0        0.0        0.0   \n", "\n", "   total_roi_adv7  total_pay_adv7  \n", "2          0.2771          3508.0  \n", "3          0.0546           168.0  \n", "5          0.0000             0.0  \n", "6          0.0000             0.0  \n", "7          0.0000             0.0  "]}, "execution_count": 260, "metadata": {}, "output_type": "execute_result"}], "source": ["actions_background.head()"]}, {"cell_type": "code", "execution_count": 300, "id": "f311a83e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["label=0时值为0的占比为98.11%\n", "label=1时值为0的占比为91.96%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_discrete_distributions(df, column_name, bins, pop_0=False):\n", "    df_0 = df[df['label'] == 0].copy()\n", "    df_1 = df[df['label'] == 1].copy()\n", "    \n", "    if pop_0:\n", "        print('label=0时值为0的占比为{}%'.format(round(df_0[df_0[column_name] == 0].shape[0] / df_0.shape[0] * 100, 2)))\n", "        print('label=1时值为0的占比为{}%'.format(round(df_1[df_1[column_name] == 0].shape[0] / df_1.shape[0] * 100, 2)))\n", "        df_0 = df_0[df_0[column_name] !=0]\n", "        df_1 = df_1[df_1[column_name] !=0]\n", "\n", "    df_0['bin'] = pd.cut(df_0[column_name], bins=bins)\n", "    df_1['bin'] = pd.cut(df_1[column_name], bins=bins)\n", "\n", "    counts_0 = (df_0['bin'].value_counts().sort_index() / len(df_0) * 100).round(2)\n", "    counts_1 = (df_1['bin'].value_counts().sort_index() / len(df_1) * 100).round(2)\n", "\n", "    # 创建单个图\n", "    plt.figure(figsize=(15, 6))\n", "    \n", "    # 获取x位置\n", "    x = np.arange(len(counts_0))\n", "    width = 0.35  # 柱子的宽度\n", "    \n", "    # 绘制两组柱状图\n", "    bar1 = plt.bar(x - width/2, counts_0, width, label='Label 0', color='skyblue', edgecolor='black')\n", "    bar2 = plt.bar(x + width/2, counts_1, width, label='Label 1', color='lightcoral', edgecolor='black')\n", "\n", "    plt.title(f'Distribution for {column_name} (%)')\n", "    plt.xlabel('Bins')\n", "    plt.ylabel('Percentage (%)')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 设置x轴刻度\n", "    plt.xticks(x, counts_0.index, rotation=45)\n", "    \n", "    # 添加图例\n", "    plt.legend()\n", "\n", "    # 在柱子上添加标签\n", "    def add_value_labels(bars):\n", "        for bar in bars:\n", "            height = bar.get_height()\n", "            plt.text(bar.get_x() + bar.get_width()/2., height,\n", "                    f'{height:.1f}%',\n", "                    ha='center', va='bottom')\n", "\n", "    add_value_labels(bar1)\n", "    add_value_labels(bar2)\n", "\n", "    # 调整y轴范围\n", "    plt.ylim(0, max(counts_0.max(), counts_1.max()) * 1.1)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "column_bins = {\n", "    # 'cost_adv7': ([0,10,20,50,100,200,500,1000,10000,100000], False),\n", "    # 'click_adv7': ([0,2,5,10,20,50,100,1000,10000,100000], False),\n", "    # 'cost_by_thousand_show_adv7': ([-1,0,20,40,70,100,1000,10000,100000], False),\n", "    # 'new_user_adv7': ([0,2,5,10,15,20,50,100,200,500], True),\n", "    # 'new_paid_user_adv7': ([0,1,2,3,4,5,6,7,8,9,10,11], True),\n", "    # 'new_user_cost_adv7': ([0,50,100,200,500,1000,2000], True),\n", "    # 'new_paid_user_cost_adv7': ([0,100,200,500,800,1200,1500,2000,10000,20000], True),\n", "    # 'new_paid_rate_adv7': ([0,0.02,0.04,0.08,0.1,0.2,0.4,0.7,1], True),\n", "    # 'roi1_adv7': ([0,0.001,0.002,0.004,0.007,0.01,0.02,0.04,0.08,0.1,0.2,0.4,0.7,1,2,10], True),\n", "    # 'roi3_adv7': ([0,0.001,0.002,0.004,0.007,0.01,0.02,0.04,0.08,0.1,0.2,0.4,0.7,1,2,10], True),\n", "    # 'roi7_adv7': ([0,0.001,0.002,0.004,0.007,0.01,0.02,0.04,0.08,0.1,0.2,0.4,0.7,1,2,10], True),\n", "    'convert_adv7': ([0,2,5,10,15,20], True),\n", "}\n", "\n", "for column, (bin, pop_0) in column_bins.items():\n", "    plot_discrete_distributions(actions_background, column, bin, pop_0)"]}, {"cell_type": "code", "execution_count": 255, "id": "34d5ea54", "metadata": {}, "outputs": [{"data": {"text/plain": ["70"]}, "execution_count": 255, "metadata": {}, "output_type": "execute_result"}], "source": ["actions_background[(actions_background['ad_id'].isna()) & (actions_background['macro_action'] == '重启广告')].shape[0]"]}, {"cell_type": "code", "execution_count": 245, "id": "d0de2793", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>operation_day</th>\n", "      <th>operation_time</th>\n", "      <th>advertiser_studio_id</th>\n", "      <th>advertiser_id</th>\n", "      <th>optimizer_name</th>\n", "      <th>channel_operator</th>\n", "      <th>operation_object_id</th>\n", "      <th>operation_object_name</th>\n", "      <th>operation_target</th>\n", "      <th>operation_type</th>\n", "      <th>operation_log</th>\n", "      <th>sub_job_id</th>\n", "      <th>execute_date</th>\n", "      <th>macro_action</th>\n", "      <th>timestamp</th>\n", "      <th>snap_day</th>\n", "      <th>snap_time</th>\n", "      <th>platform_id</th>\n", "      <th>channel_id</th>\n", "      <th>ad_id</th>\n", "      <th>ad_activation</th>\n", "      <th>ad_bid</th>\n", "      <th>ad_budget</th>\n", "      <th>ad_c</th>\n", "      <th>ad_i</th>\n", "      <th>ad_pay1</th>\n", "      <th>ad_pay_user</th>\n", "      <th>ad_roi1</th>\n", "      <th>ad_roi_bid</th>\n", "      <th>ad_s</th>\n", "      <th>ad_status</th>\n", "      <th>cost_adv7</th>\n", "      <th>show_adv7</th>\n", "      <th>click_adv7</th>\n", "      <th>cost_by_thousand_show_adv7</th>\n", "      <th>new_user_adv7</th>\n", "      <th>new_paid_user_adv7</th>\n", "      <th>new_user_cost_adv7</th>\n", "      <th>new_paid_user_cost_adv7</th>\n", "      <th>roi1_adv7</th>\n", "      <th>roi7_adv7</th>\n", "      <th>convert_adv7</th>\n", "      <th>active_adv7</th>\n", "      <th>convert_cost_adv7</th>\n", "      <th>active_cost_adv7</th>\n", "      <th>ctr_adv7</th>\n", "      <th>cvr_adv7</th>\n", "      <th>new_paid_rate_adv7</th>\n", "      <th>roi3_adv7</th>\n", "      <th>pay1_adv7</th>\n", "      <th>pay3_adv7</th>\n", "      <th>pay7_adv7</th>\n", "      <th>total_roi_adv7</th>\n", "      <th>total_pay_adv7</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>2025-02-25</td>\n", "      <td>09:38:18</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1824474726398075</td>\n", "      <td>刘雨晴</td>\n", "      <td><EMAIL></td>\n", "      <td>7473770230079848499</td>\n", "      <td>0221-152327-13</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-02-25 09:38:18</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>484</th>\n", "      <td>2025-02-25</td>\n", "      <td>11:39:58</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1824474726398075</td>\n", "      <td>刘雨晴</td>\n", "      <td><EMAIL></td>\n", "      <td>7473770248194555916</td>\n", "      <td>0221-152327-31</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-02-25 11:39:58</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>487</th>\n", "      <td>2025-02-25</td>\n", "      <td>11:40:32</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1824474726398075</td>\n", "      <td>刘雨晴</td>\n", "      <td><EMAIL></td>\n", "      <td>7474885372528820250</td>\n", "      <td>0224-153034-19</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-02-25 11:40:32</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>488</th>\n", "      <td>2025-02-25</td>\n", "      <td>11:40:34</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1824474726398075</td>\n", "      <td>刘雨晴</td>\n", "      <td><EMAIL></td>\n", "      <td>7474885271202742309</td>\n", "      <td>0224-153034-31</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-02-25 11:40:34</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>765</th>\n", "      <td>2025-02-27</td>\n", "      <td>09:35:20</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1824474727228755</td>\n", "      <td>刘雨晴</td>\n", "      <td><EMAIL></td>\n", "      <td>7474823070256594985</td>\n", "      <td>0224-111801-6</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 暂停 -&gt; 正常]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>重启广告</td>\n", "      <td>2025-02-27 09:35:20</td>\n", "      <td>2025-02-26</td>\n", "      <td>23:53:25</td>\n", "      <td>1.0</td>\n", "      <td>trade</td>\n", "      <td>7.474823e+18</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>390.0</td>\n", "      <td>52962.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1527.4</td>\n", "      <td>已暂停</td>\n", "      <td>2874.78</td>\n", "      <td>72976.0</td>\n", "      <td>453.0</td>\n", "      <td>39.39</td>\n", "      <td>8.0</td>\n", "      <td>1.0</td>\n", "      <td>359.35</td>\n", "      <td>2874.78</td>\n", "      <td>0.0104</td>\n", "      <td>0.0104</td>\n", "      <td>2.0</td>\n", "      <td>13.0</td>\n", "      <td>1437.39</td>\n", "      <td>221.14</td>\n", "      <td>0.0062</td>\n", "      <td>0.0177</td>\n", "      <td>0.125</td>\n", "      <td>0.0104</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>0.0104</td>\n", "      <td>30.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    operation_day operation_time advertiser_studio_id     advertiser_id  \\\n", "62     2025-02-25       09:38:18                灵眸工作室  1824474726398075   \n", "484    2025-02-25       11:39:58                灵眸工作室  1824474726398075   \n", "487    2025-02-25       11:40:32                灵眸工作室  1824474726398075   \n", "488    2025-02-25       11:40:34                灵眸工作室  1824474726398075   \n", "765    2025-02-27       09:35:20                灵眸工作室  1824474727228755   \n", "\n", "    optimizer_name     channel_operator  operation_object_id  \\\n", "62             刘雨晴  <EMAIL>  7473770230079848499   \n", "484            刘雨晴  <EMAIL>  7473770248194555916   \n", "487            刘雨晴  <EMAIL>  7474885372528820250   \n", "488            刘雨晴  <EMAIL>  7474885271202742309   \n", "765            刘雨晴  <EMAIL>  7474823070256594985   \n", "\n", "    operation_object_name operation_target operation_type  \\\n", "62         0221-152327-13               广告             修改   \n", "484        0221-152327-31               广告             修改   \n", "487        0224-153034-19               广告             修改   \n", "488        0224-153034-31               广告             修改   \n", "765         0224-111801-6               广告             修改   \n", "\n", "           operation_log  sub_job_id execute_date macro_action  \\\n", "62   [修改 操作状态: 暂停 -> 正常]        1573   2025-05-19         重启广告   \n", "484  [修改 操作状态: 暂停 -> 正常]        1573   2025-05-19         重启广告   \n", "487  [修改 操作状态: 暂停 -> 正常]        1573   2025-05-19         重启广告   \n", "488  [修改 操作状态: 暂停 -> 正常]        1573   2025-05-19         重启广告   \n", "765  [修改 操作状态: 暂停 -> 正常]        1573   2025-05-19         重启广告   \n", "\n", "              timestamp    snap_day snap_time  platform_id channel_id  \\\n", "62  2025-02-25 09:38:18         NaN       NaN          NaN        NaN   \n", "484 2025-02-25 11:39:58         NaN       NaN          NaN        NaN   \n", "487 2025-02-25 11:40:32         NaN       NaN          NaN        NaN   \n", "488 2025-02-25 11:40:34         NaN       NaN          NaN        NaN   \n", "765 2025-02-27 09:35:20  2025-02-26  23:53:25          1.0      trade   \n", "\n", "            ad_id  ad_activation  ad_bid  ad_budget   ad_c     ad_i  ad_pay1  \\\n", "62            NaN            NaN     NaN        NaN    NaN      NaN      NaN   \n", "484           NaN            NaN     NaN        NaN    NaN      NaN      NaN   \n", "487           NaN            NaN     NaN        NaN    NaN      NaN      NaN   \n", "488           NaN            NaN     NaN        NaN    NaN      NaN      NaN   \n", "765  7.474823e+18            5.0     0.0        0.0  390.0  52962.0      0.0   \n", "\n", "     ad_pay_user  ad_roi1  ad_roi_bid    ad_s ad_status  cost_adv7  show_adv7  \\\n", "62           NaN      NaN         NaN     NaN       NaN        NaN        NaN   \n", "484          NaN      NaN         NaN     NaN       NaN        NaN        NaN   \n", "487          NaN      NaN         NaN     NaN       NaN        NaN        NaN   \n", "488          NaN      NaN         NaN     NaN       NaN        NaN        NaN   \n", "765          0.0      0.0         0.0  1527.4       已暂停    2874.78    72976.0   \n", "\n", "     click_adv7  cost_by_thousand_show_adv7  new_user_adv7  \\\n", "62          NaN                         NaN            NaN   \n", "484         NaN                         NaN            NaN   \n", "487         NaN                         NaN            NaN   \n", "488         NaN                         NaN            NaN   \n", "765       453.0                       39.39            8.0   \n", "\n", "     new_paid_user_adv7  new_user_cost_adv7  new_paid_user_cost_adv7  \\\n", "62                  NaN                 NaN                      NaN   \n", "484                 NaN                 NaN                      NaN   \n", "487                 NaN                 NaN                      NaN   \n", "488                 NaN                 NaN                      NaN   \n", "765                 1.0              359.35                  2874.78   \n", "\n", "     roi1_adv7  roi7_adv7  convert_adv7  active_adv7  convert_cost_adv7  \\\n", "62         NaN        NaN           NaN          NaN                NaN   \n", "484        NaN        NaN           NaN          NaN                NaN   \n", "487        NaN        NaN           NaN          NaN                NaN   \n", "488        NaN        NaN           NaN          NaN                NaN   \n", "765     0.0104     0.0104           2.0         13.0            1437.39   \n", "\n", "     active_cost_adv7  ctr_adv7  cvr_adv7  new_paid_rate_adv7  roi3_adv7  \\\n", "62                NaN       NaN       NaN                 NaN        NaN   \n", "484               NaN       NaN       NaN                 NaN        NaN   \n", "487               NaN       NaN       NaN                 NaN        NaN   \n", "488               NaN       NaN       NaN                 NaN        NaN   \n", "765            221.14    0.0062    0.0177               0.125     0.0104   \n", "\n", "     pay1_adv7  pay3_adv7  pay7_adv7  total_roi_adv7  total_pay_adv7  \n", "62         NaN        NaN        NaN             NaN             NaN  \n", "484        NaN        NaN        NaN             NaN             NaN  \n", "487        NaN        NaN        NaN             NaN             NaN  \n", "488        NaN        NaN        NaN             NaN             NaN  \n", "765       30.0       30.0       30.0          0.0104            30.0  "]}, "execution_count": 245, "metadata": {}, "output_type": "execute_result"}], "source": ["actions_background[actions_background['macro_action'] == '重启广告'].head()"]}, {"cell_type": "code", "execution_count": 243, "id": "2414c64f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>operation_day</th>\n", "      <th>operation_time</th>\n", "      <th>advertiser_studio_id</th>\n", "      <th>advertiser_id</th>\n", "      <th>optimizer_name</th>\n", "      <th>channel_operator</th>\n", "      <th>operation_object_id</th>\n", "      <th>operation_object_name</th>\n", "      <th>operation_target</th>\n", "      <th>operation_type</th>\n", "      <th>operation_log</th>\n", "      <th>sub_job_id</th>\n", "      <th>execute_date</th>\n", "      <th>macro_action</th>\n", "      <th>timestamp</th>\n", "      <th>snap_day</th>\n", "      <th>snap_time</th>\n", "      <th>platform_id</th>\n", "      <th>channel_id</th>\n", "      <th>ad_id</th>\n", "      <th>ad_activation</th>\n", "      <th>ad_bid</th>\n", "      <th>ad_budget</th>\n", "      <th>ad_c</th>\n", "      <th>ad_i</th>\n", "      <th>ad_pay1</th>\n", "      <th>ad_pay_user</th>\n", "      <th>ad_roi1</th>\n", "      <th>ad_roi_bid</th>\n", "      <th>ad_s</th>\n", "      <th>ad_status</th>\n", "      <th>cost_adv7</th>\n", "      <th>show_adv7</th>\n", "      <th>click_adv7</th>\n", "      <th>cost_by_thousand_show_adv7</th>\n", "      <th>new_user_adv7</th>\n", "      <th>new_paid_user_adv7</th>\n", "      <th>new_user_cost_adv7</th>\n", "      <th>new_paid_user_cost_adv7</th>\n", "      <th>roi1_adv7</th>\n", "      <th>roi7_adv7</th>\n", "      <th>convert_adv7</th>\n", "      <th>active_adv7</th>\n", "      <th>convert_cost_adv7</th>\n", "      <th>active_cost_adv7</th>\n", "      <th>ctr_adv7</th>\n", "      <th>cvr_adv7</th>\n", "      <th>new_paid_rate_adv7</th>\n", "      <th>roi3_adv7</th>\n", "      <th>pay1_adv7</th>\n", "      <th>pay3_adv7</th>\n", "      <th>pay7_adv7</th>\n", "      <th>total_roi_adv7</th>\n", "      <th>total_pay_adv7</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-02-25</td>\n", "      <td>08:14:13</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1762935596595341</td>\n", "      <td>王雅涛</td>\n", "      <td><EMAIL></td>\n", "      <td>7473812361553657910</td>\n", "      <td>站内排除-40包roi-0221-4</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 正常 -&gt; 暂停]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 08:14:13</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-02-25</td>\n", "      <td>08:14:13</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1762935596595341</td>\n", "      <td>王雅涛</td>\n", "      <td><EMAIL></td>\n", "      <td>7473455079909408779</td>\n", "      <td>站内排除-40包roi-0220-15</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 正常 -&gt; 暂停]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 08:14:13</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-02-25</td>\n", "      <td>08:14:14</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1762935596595341</td>\n", "      <td>王雅涛</td>\n", "      <td><EMAIL></td>\n", "      <td>7473455169049772071</td>\n", "      <td>站内排除-40包roi-0220-60</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 正常 -&gt; 暂停]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 08:14:14</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-02-25</td>\n", "      <td>08:14:14</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1762935596595341</td>\n", "      <td>王雅涛</td>\n", "      <td><EMAIL></td>\n", "      <td>7473812448434208787</td>\n", "      <td>站内排除-40包roi-0221-62</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 正常 -&gt; 暂停]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 08:14:14</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-02-25</td>\n", "      <td>08:14:15</td>\n", "      <td>灵眸工作室</td>\n", "      <td>1762935596595341</td>\n", "      <td>王雅涛</td>\n", "      <td><EMAIL></td>\n", "      <td>7473812422194348059</td>\n", "      <td>站内排除-40包roi-0221-25</td>\n", "      <td>广告</td>\n", "      <td>修改</td>\n", "      <td>[修改 操作状态: 正常 -&gt; 暂停]</td>\n", "      <td>1573</td>\n", "      <td>2025-05-19</td>\n", "      <td>暂停广告</td>\n", "      <td>2025-02-25 08:14:15</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  operation_day operation_time advertiser_studio_id     advertiser_id  \\\n", "0    2025-02-25       08:14:13                灵眸工作室  1762935596595341   \n", "1    2025-02-25       08:14:13                灵眸工作室  1762935596595341   \n", "2    2025-02-25       08:14:14                灵眸工作室  1762935596595341   \n", "3    2025-02-25       08:14:14                灵眸工作室  1762935596595341   \n", "4    2025-02-25       08:14:15                灵眸工作室  1762935596595341   \n", "\n", "  optimizer_name     channel_operator  operation_object_id  \\\n", "0            王雅涛  <EMAIL>  7473812361553657910   \n", "1            王雅涛  <EMAIL>  7473455079909408779   \n", "2            王雅涛  <EMAIL>  7473455169049772071   \n", "3            王雅涛  <EMAIL>  7473812448434208787   \n", "4            王雅涛  <EMAIL>  7473812422194348059   \n", "\n", "  operation_object_name operation_target operation_type        operation_log  \\\n", "0    站内排除-40包roi-0221-4               广告             修改  [修改 操作状态: 正常 -> 暂停]   \n", "1   站内排除-40包roi-0220-15               广告             修改  [修改 操作状态: 正常 -> 暂停]   \n", "2   站内排除-40包roi-0220-60               广告             修改  [修改 操作状态: 正常 -> 暂停]   \n", "3   站内排除-40包roi-0221-62               广告             修改  [修改 操作状态: 正常 -> 暂停]   \n", "4   站内排除-40包roi-0221-25               广告             修改  [修改 操作状态: 正常 -> 暂停]   \n", "\n", "   sub_job_id execute_date macro_action           timestamp snap_day  \\\n", "0        1573   2025-05-19         暂停广告 2025-02-25 08:14:13      NaN   \n", "1        1573   2025-05-19         暂停广告 2025-02-25 08:14:13      NaN   \n", "2        1573   2025-05-19         暂停广告 2025-02-25 08:14:14      NaN   \n", "3        1573   2025-05-19         暂停广告 2025-02-25 08:14:14      NaN   \n", "4        1573   2025-05-19         暂停广告 2025-02-25 08:14:15      NaN   \n", "\n", "  snap_time  platform_id channel_id  ad_id  ad_activation  ad_bid  ad_budget  \\\n", "0       NaN          NaN        NaN    NaN            NaN     NaN        NaN   \n", "1       NaN          NaN        NaN    NaN            NaN     NaN        NaN   \n", "2       NaN          NaN        NaN    NaN            NaN     NaN        NaN   \n", "3       NaN          NaN        NaN    NaN            NaN     NaN        NaN   \n", "4       NaN          NaN        NaN    NaN            NaN     NaN        NaN   \n", "\n", "   ad_c  ad_i  ad_pay1  ad_pay_user  ad_roi1  ad_roi_bid  ad_s ad_status  \\\n", "0   NaN   NaN      NaN          NaN      NaN         NaN   NaN       NaN   \n", "1   NaN   NaN      NaN          NaN      NaN         NaN   NaN       NaN   \n", "2   NaN   NaN      NaN          NaN      NaN         NaN   NaN       NaN   \n", "3   NaN   NaN      NaN          NaN      NaN         NaN   NaN       NaN   \n", "4   NaN   NaN      NaN          NaN      NaN         NaN   NaN       NaN   \n", "\n", "   cost_adv7  show_adv7  click_adv7  cost_by_thousand_show_adv7  \\\n", "0        NaN        NaN         NaN                         NaN   \n", "1        NaN        NaN         NaN                         NaN   \n", "2        NaN        NaN         NaN                         NaN   \n", "3        NaN        NaN         NaN                         NaN   \n", "4        NaN        NaN         NaN                         NaN   \n", "\n", "   new_user_adv7  new_paid_user_adv7  new_user_cost_adv7  \\\n", "0            NaN                 NaN                 NaN   \n", "1            NaN                 NaN                 NaN   \n", "2            NaN                 NaN                 NaN   \n", "3            NaN                 NaN                 NaN   \n", "4            NaN                 NaN                 NaN   \n", "\n", "   new_paid_user_cost_adv7  roi1_adv7  roi7_adv7  convert_adv7  active_adv7  \\\n", "0                      NaN        NaN        NaN           NaN          NaN   \n", "1                      NaN        NaN        NaN           NaN          NaN   \n", "2                      NaN        NaN        NaN           NaN          NaN   \n", "3                      NaN        NaN        NaN           NaN          NaN   \n", "4                      NaN        NaN        NaN           NaN          NaN   \n", "\n", "   convert_cost_adv7  active_cost_adv7  ctr_adv7  cvr_adv7  \\\n", "0                NaN               NaN       NaN       NaN   \n", "1                NaN               NaN       NaN       NaN   \n", "2                NaN               NaN       NaN       NaN   \n", "3                NaN               NaN       NaN       NaN   \n", "4                NaN               NaN       NaN       NaN   \n", "\n", "   new_paid_rate_adv7  roi3_adv7  pay1_adv7  pay3_adv7  pay7_adv7  \\\n", "0                 NaN        NaN        NaN        NaN        NaN   \n", "1                 NaN        NaN        NaN        NaN        NaN   \n", "2                 NaN        NaN        NaN        NaN        NaN   \n", "3                 NaN        NaN        NaN        NaN        NaN   \n", "4                 NaN        NaN        NaN        NaN        NaN   \n", "\n", "   total_roi_adv7  total_pay_adv7  \n", "0             NaN             NaN  \n", "1             NaN             NaN  \n", "2             NaN             NaN  \n", "3             NaN             NaN  \n", "4             NaN             NaN  "]}, "execution_count": 243, "metadata": {}, "output_type": "execute_result"}], "source": ["actions_background.head()"]}, {"cell_type": "code", "execution_count": 163, "id": "fe53af3d", "metadata": {}, "outputs": [], "source": ["# 合并宏动作\n", "start_actions_sorted = start_actions_background.sort_values(['optimizer_name', 'timestamp'])\n", "start_actions_sorted['time_diff'] = start_actions_sorted.groupby('optimizer_name')['timestamp'].diff().dt.total_seconds()\n", "optimizer_change = (start_actions_sorted['optimizer_name'] != start_actions_sorted['optimizer_name'].shift()).astype(int)\n", "start_actions_sorted['action_group'] =  ((start_actions_sorted['time_diff'] > 600) | (start_actions_sorted['optimizer_name'] != start_actions_sorted['optimizer_name'].shift())).astype(int).cumsum()\n", "start_actions_sorted['macro_timestamp'] = start_actions_sorted.groupby('action_group')['timestamp'].transform('first')\n", "start_actions_sorted = start_actions_sorted.drop(['time_diff', 'timestamp'], axis=1)"]}, {"cell_type": "code", "execution_count": 164, "id": "e6a07cb3", "metadata": {}, "outputs": [], "source": ["# 添加大盘数据\n", "market_snapshot_df_complete['snap_timestamp'] = pd.to_datetime(market_snapshot_df_complete['snap_day'].astype(str) + ' ' + market_snapshot_df_complete['snap_time'].astype(str))\n", "\n", "start_actions_sorted = start_actions_sorted.sort_values('macro_timestamp')\n", "market_snapshot_df_complete = market_snapshot_df_complete.sort_values('snap_timestamp')\n", "\n", "data_start_actions = pd.merge_asof(\n", "    start_actions_sorted,\n", "    market_snapshot_df_complete.drop(['snap_day', 'snap_time'], axis=1),\n", "    left_on='macro_timestamp',\n", "    right_on='snap_timestamp',\n", "    direction='backward'\n", ")"]}, {"cell_type": "code", "execution_count": 165, "id": "f49009b0", "metadata": {}, "outputs": [], "source": ["start_timestamps = data_start_actions['snap_timestamp'].unique()\n", "adv_market_df = pd.merge_asof(\n", "    adv_df.sort_values('timestamp'),\n", "    market_snapshot_df_complete.sort_values('snap_timestamp').drop(['snap_day', 'snap_time'], axis=1),\n", "    left_on='timestamp',\n", "    right_on='snap_timestamp'\n", ")"]}, {"cell_type": "code", "execution_count": 166, "id": "845f01f9", "metadata": {}, "outputs": [], "source": ["data_start_actions['match_key'] = data_start_actions['snap_timestamp'].astype(str) + '_' + data_start_actions['operation_object_id'].astype(str)\n", "actioned_adv_market_df = adv_market_df[adv_market_df['timestamp'].isin(data_start_actions['snap_timestamp'])].copy()\n", "actioned_adv_market_df['match_key'] = actioned_adv_market_df['timestamp'].astype(str) + '_' + actioned_adv_market_df['ad_id'].astype(str)\n", "actioned_adv_market_df['label'] = actioned_adv_market_df['match_key'].isin(data_start_actions['match_key']).astype(int)"]}, {"cell_type": "code", "execution_count": 170, "id": "64680d96", "metadata": {}, "outputs": [{"data": {"text/plain": ["1451"]}, "execution_count": 170, "metadata": {}, "output_type": "execute_result"}], "source": ["start_actions.shape[0]"]}, {"cell_type": "markdown", "id": "7b9a49bf", "metadata": {}, "source": ["## 将excel转换成csv"]}, {"cell_type": "code", "execution_count": null, "id": "d5a5ac3f", "metadata": {}, "outputs": [], "source": ["from openpyxl import Workbook, load_workbook\n", "import pandas as pd\n", "\n", "\n", "file_path = r\"/home/<USER>/operations/data/adv_history.xlsx\"\n", "workbook = load_workbook(file_path, read_only=True)\n", "sheet = workbook['Sheet1']\n", "\n", "header_path = \"/home/<USER>/operations/data/header.xlsx\"\n", "header_workbook = load_workbook(header_path, read_only=True)\n", "header = list(header_workbook['Sheet1'].values)[0]\n", "\n", "history_df = pd.DataFrame(sheet.values, columns=header)"]}, {"cell_type": "code", "execution_count": null, "id": "cce2441d", "metadata": {}, "outputs": [], "source": ["adv_history_df = adv_history_df[adv_history_df['广告id'] != '0']\n", "adv_history_df = adv_history_df.sort_values(['广告id', '日期'])\n", "adv_history_df.to_csv('../data/adv_history.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "211f7076", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>日期</th>\n", "      <th>广告id</th>\n", "      <th>消耗</th>\n", "      <th>展示</th>\n", "      <th>点击</th>\n", "      <th>千次展示成本</th>\n", "      <th>新增设备数</th>\n", "      <th>首日付费数</th>\n", "      <th>新增成本</th>\n", "      <th>新增付费成本</th>\n", "      <th>首日ROI</th>\n", "      <th>2日留存</th>\n", "      <th>7日ROI</th>\n", "      <th>转化</th>\n", "      <th>激活</th>\n", "      <th>转化成本</th>\n", "      <th>激活成本</th>\n", "      <th>CTR</th>\n", "      <th>CVR</th>\n", "      <th>新增付费率rpu</th>\n", "      <th>3日ROI</th>\n", "      <th>1日付费</th>\n", "      <th>3日付费</th>\n", "      <th>7日付费</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>789</th>\n", "      <td>2025-02-18</td>\n", "      <td>7338016007317766170</td>\n", "      <td>4122.27</td>\n", "      <td>37946</td>\n", "      <td>473</td>\n", "      <td>108.6352</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>412.2270</td>\n", "      <td>4122.27</td>\n", "      <td>0.0015</td>\n", "      <td>0.2000</td>\n", "      <td>0.2499</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "      <td>4122.270</td>\n", "      <td>317.0977</td>\n", "      <td>0.0125</td>\n", "      <td>0.0021</td>\n", "      <td>0.1000</td>\n", "      <td>0.0771</td>\n", "      <td>6</td>\n", "      <td>318</td>\n", "      <td>1030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2705</th>\n", "      <td>2025-02-19</td>\n", "      <td>7338016007317766170</td>\n", "      <td>1541.11</td>\n", "      <td>47688</td>\n", "      <td>583</td>\n", "      <td>32.3165</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>220.1586</td>\n", "      <td>1541.11</td>\n", "      <td>0.0078</td>\n", "      <td>0.2857</td>\n", "      <td>0.0117</td>\n", "      <td>5</td>\n", "      <td>12</td>\n", "      <td>308.222</td>\n", "      <td>128.4258</td>\n", "      <td>0.0122</td>\n", "      <td>0.0086</td>\n", "      <td>0.1429</td>\n", "      <td>0.0117</td>\n", "      <td>12</td>\n", "      <td>18</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261252</th>\n", "      <td>2025-02-20</td>\n", "      <td>7338016007317766170</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>0.000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>247981</th>\n", "      <td>2025-02-21</td>\n", "      <td>7338016007317766170</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0.000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>247979</th>\n", "      <td>2025-02-23</td>\n", "      <td>7338016007317766170</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0.000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                日期                 广告id       消耗     展示   点击    千次展示成本  新增设备数  \\\n", "789     2025-02-18  7338016007317766170  4122.27  37946  473  108.6352     10   \n", "2705    2025-02-19  7338016007317766170  1541.11  47688  583   32.3165      7   \n", "261252  2025-02-20  7338016007317766170     0.00      0    0    0.0000      1   \n", "247981  2025-02-21  7338016007317766170     0.00      0    0    0.0000      1   \n", "247979  2025-02-23  7338016007317766170     0.00      0    0    0.0000      0   \n", "\n", "        首日付费数      新增成本   新增付费成本   首日ROI    2日留存   7日ROI  转化  激活      转化成本  \\\n", "789         1  412.2270  4122.27  0.0015  0.2000  0.2499   1  13  4122.270   \n", "2705        1  220.1586  1541.11  0.0078  0.2857  0.0117   5  12   308.222   \n", "261252      0    0.0000     0.00  0.0000  0.0000  0.0000   2   3     0.000   \n", "247981      0    0.0000     0.00  0.0000  0.0000  0.0000   0   1     0.000   \n", "247979      0    0.0000     0.00  0.0000  0.0000  0.0000   0   1     0.000   \n", "\n", "            激活成本     CTR     CVR  新增付费率rpu   3日ROI  1日付费  3日付费  7日付费  \n", "789     317.0977  0.0125  0.0021    0.1000  0.0771     6   318  1030  \n", "2705    128.4258  0.0122  0.0086    0.1429  0.0117    12    18    18  \n", "261252    0.0000  0.0000  0.0000    0.0000  0.0000     0     0     0  \n", "247981    0.0000  0.0000  0.0000    0.0000  0.0000     0     0     0  \n", "247979    0.0000  0.0000  0.0000    0.0000  0.0000     0     0     0  "]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["adv_history_df.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "75346dbb", "metadata": {}, "outputs": [], "source": ["from openpyxl import Workbook, load_workbook\n", "import pandas as pd\n", "\n", "\n", "file_path = r\"/home/<USER>/operations/data/daily_adv_ids.xlsx\"\n", "workbook = load_workbook(file_path, read_only=True)\n", "sheet = workbook['Sheet1']\n", "\n", "adv_daily_ids_df = pd.DataFrame(sheet.values, columns=['date', 'ad_id'])\n", "adv_daily_ids_df.head()\n", "adv_daily_ids_df.to_csv('../data/daily_adv_ids.csv', index=False)"]}, {"cell_type": "markdown", "id": "8d05e115", "metadata": {}, "source": ["## 从所有snapshot中提取3D捕鱼的快照"]}, {"cell_type": "code", "execution_count": null, "id": "7f541d23", "metadata": {}, "outputs": [], "source": ["daily_adv_ids_path = '/home/<USER>/operations/data/daily_adv_ids.csv'\n", "daily_adv_ids_df = pd.read_csv(daily_adv_ids_path, on_bad_lines='skip')\n", "\n", "adv_snapshot_file_path = \"/home/<USER>/operations/data/realtime_snapshot.csv\"\n", "adv_snapshot_df = pd.read_csv(adv_snapshot_file_path, on_bad_lines='skip')\n", "adv_snapshot_df = adv_snapshot_df.drop_duplicates(subset=['snap_day', 'snap_time', 'ad_id'])\n", "adv_snapshot_df = adv_snapshot_df.drop(columns=['media_source', 'sub_job_id', 'execute_date'], errors='ignore')"]}, {"cell_type": "code", "execution_count": 6, "id": "ca188475", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>snap_day</th>\n", "      <th>snap_time</th>\n", "      <th>platform_id</th>\n", "      <th>channel_id</th>\n", "      <th>ad_id</th>\n", "      <th>ad_activation</th>\n", "      <th>ad_bid</th>\n", "      <th>ad_budget</th>\n", "      <th>ad_c</th>\n", "      <th>ad_i</th>\n", "      <th>ad_pay1</th>\n", "      <th>ad_pay_user</th>\n", "      <th>ad_roi1</th>\n", "      <th>ad_roi_bid</th>\n", "      <th>ad_s</th>\n", "      <th>ad_status</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-03-15</td>\n", "      <td>05:16:19</td>\n", "      <td>1</td>\n", "      <td>trade</td>\n", "      <td>8380765502</td>\n", "      <td>1</td>\n", "      <td>智能出价</td>\n", "      <td>不限</td>\n", "      <td>1</td>\n", "      <td>86</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>5.585</td>\n", "      <td>有效</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-03-14</td>\n", "      <td>19:16:18</td>\n", "      <td>1</td>\n", "      <td>trade</td>\n", "      <td>8402494010</td>\n", "      <td>0</td>\n", "      <td>智能出价</td>\n", "      <td>不限</td>\n", "      <td>1</td>\n", "      <td>95</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>9.027</td>\n", "      <td>有效</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-03-14</td>\n", "      <td>19:16:18</td>\n", "      <td>1</td>\n", "      <td>trade</td>\n", "      <td>8395472206</td>\n", "      <td>0</td>\n", "      <td>200</td>\n", "      <td>不限</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>0.091</td>\n", "      <td>有效</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-03-15</td>\n", "      <td>05:16:19</td>\n", "      <td>1</td>\n", "      <td>trade</td>\n", "      <td>8384413245</td>\n", "      <td>0</td>\n", "      <td>智能出价</td>\n", "      <td>不限</td>\n", "      <td>6</td>\n", "      <td>3971</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>211.253</td>\n", "      <td>有效</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-03-14</td>\n", "      <td>19:16:18</td>\n", "      <td>1</td>\n", "      <td>trade</td>\n", "      <td>8395469822</td>\n", "      <td>0</td>\n", "      <td>200</td>\n", "      <td>不限</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.05</td>\n", "      <td>0.127</td>\n", "      <td>有效</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     snap_day snap_time  platform_id channel_id       ad_id  ad_activation  \\\n", "0  2025-03-15  05:16:19            1      trade  8380765502              1   \n", "1  2025-03-14  19:16:18            1      trade  8402494010              0   \n", "2  2025-03-14  19:16:18            1      trade  8395472206              0   \n", "3  2025-03-15  05:16:19            1      trade  8384413245              0   \n", "4  2025-03-14  19:16:18            1      trade  8395469822              0   \n", "\n", "  ad_bid ad_budget  ad_c  ad_i  ad_pay1  ad_pay_user  ad_roi1  ad_roi_bid  \\\n", "0   智能出价        不限     1    86        0            0      0.0        0.00   \n", "1   智能出价        不限     1    95        0            0      0.0        0.00   \n", "2    200        不限     0     2        0            0      0.0        0.05   \n", "3   智能出价        不限     6  3971        0            0      0.0        0.00   \n", "4    200        不限     0     5        0            0      0.0        0.05   \n", "\n", "      ad_s ad_status  \n", "0    5.585        有效  \n", "1    9.027        有效  \n", "2    0.091        有效  \n", "3  211.253        有效  \n", "4    0.127        有效  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["adv_snapshot_df.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "a08b3658", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>ad_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-04-24</td>\n", "      <td>7495725128476442634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-04-28</td>\n", "      <td>7496027765866463259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-04-24</td>\n", "      <td>7496021852004122665</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-04-03</td>\n", "      <td>7488614504336719898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-04-29</td>\n", "      <td>7496760662853550116</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date                ad_id\n", "0  2025-04-24  7495725128476442634\n", "1  2025-04-28  7496027765866463259\n", "2  2025-04-24  7496021852004122665\n", "3  2025-04-03  7488614504336719898\n", "4  2025-04-29  7496760662853550116"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["daily_adv_ids_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "837a3886", "metadata": {}, "outputs": [], "source": ["condition = adv_snapshot_df.set_index(['snap_day', 'ad_id']).index.isin(\n", "    daily_adv_ids_df.set_index(['date', 'ad_id']).index\n", ")\n", "\n", "adv_snapshot_df_3d_buyu = adv_snapshot_df[condition].reindex()\n", "adv_snapshot_df_3d_buyu.to_csv('../data/realtime_snapshot_3d.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}