{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["552 {30641293318, 47675562005, 24284309527, 44409956377, 47638913052, 30822303799, 47516469317, 40559296585, 34177894476, 40335149132, 45240813644, 47685779535, 36063402064, 46511042640, 28977326168, 45240813658, 47139592283, 47674654812, 47380424798, 47380459615, 43587764320, 47692554339, 46427826276, 38719676517, 37645758564, 40182837351, 44451471468, 46977658989, 21478604919, 34105774200, 47676670083, 46677397639, 47140909196, 47692339341, 45952249999, 44590649488, 46512040083, 47139592345, 47139592350, 46658107551, 45240813728, 42971463846, 47699091626, 47374340271, 47643166897, 30641291442, 47698065589, 17246314678, 47374289080, 39694512313, 46789036218, 45955887291, 29177534656, 37608612042, 20817213642, 46511042767, 47139592401, 47139592402, 47703044332, 47685349619, 47682085108, 42034827513, 46972078330, 47699935486, 44451471614, 47699138818, 46663809283, 41796987146, 19994489102, 38572462350, 44451471630, 47487924500, 47120240918, 42955192599, 44400628002, 45948272950, 47137788216, 24017461561, 34175265082, 47698993466, 47676918076, 46677610814, 47383861570, 35096070467, 47699589447, 46681547083, 47698989388, 47323849042, 46680699219, 33265981781, 41935393110, 47699435868, 47702956385, 47139950948, 17372008806, 37609914728, 30245745015, 47382929785, 46702596479, 46511147392, 47487742338, 47139592585, 47127433614, 20304079249, 29759613332, 44760232342, 46702805416, 47691887017, 47003320745, 41797085611, 43586865581, 47699038642, 47639130550, 18149276089, 47371076026, 47515034041, 46680725949, 47699192258, 20303727043, 37633696198, 46662334920, 46677610954, 47120241106, 30396944852, 46680697305, 43602657765, 41047368167, 47675458026, 29814010348, 43857023470, 46510004719, 38825585145, 47377215995, 47139592700, 47380423176, 46662435342, 37445435923, 20922784294, 47323781670, 46019021352, 35119968815, 46977477169, 44622375479, 44445436472, 47699436088, 46562755135, 43585808960, 47698844224, 47682624064, 41819351619, 47682200136, 47137716808, 43077968457, 34107574864, 47675902546, 47702733397, 44763728476, 44622365277, 42197500509, 47383540317, 47700826740, 47487951476, 44400126580, 45956278904, 16671582848, 47137716865, 41819351686, 20735117962, 42662318731, 44622320268, 46684836498, 44760230547, 47120241301, 42903607961, 44590650012, 47639093924, 44622320293, 47380685507, 45944212167, 37452571339, 47140954830, 16749126356, 40176790231, 47376317147, 44590662366, 45626147554, 46963997411, 47516517095, 47681774313, 29857465070, 40655348469, 46977927927, 37155259128, 47698993929, 47377695498, 35119856395, 47383214860, 26302419737, 25402143513, 42194025245, 46823019315, 45925126964, 42947670841, 45652814651, 47516515137, 26326471492, 47378326341, 16086713170, 47136766803, 46963667798, 47487740759, 45948701528, 37609915226, 47376495452, 40332700509, 47702874977, 21980214117, 47487740778, 47692174188, 45952576373, 47377748853, 46663805817, 20933604220, 47676502909, 47673774977, 44030419844, 47098385286, 47487740807, 43602723718, 46962674580, 47682429846, 21542648727, 41933605788, 26871995294, 43076801443, 45630933924, 47139951528, 47137815470, 44760226735, 41811809200, 45920326577, 44735767475, 47690988468, 27591170997, 46972408756, 47136633787, 47376217021, 47692012477, 47702832063, 46512239553, 47692616641, 38399247309, 47324175309, 47698793422, 47703094224, 32137327568, 47487740883, 47642563544, 47643532256, 45944683489, 21527055333, 47383374828, 47487740911, 32244816880, 44408374265, 47691203579, 38412753918, 45948333055, 36168756224, 47140596736, 46963997694, 47137641476, 46968605701, 46427929609, 47677584393, 47644111883, 46663803913, 44030614545, 34105773075, 47692962837, 47127434264, 46963438623, 47376317476, 47376636966, 47487898663, 44454868008, 35119934504, 46419450925, 47487740973, 46681541680, 47383032881, 46510064695, 46417011769, 47374664765, 47137692736, 47098700872, 46665366604, 37455852623, 38412833872, 47699043408, 41819352146, 45944130646, 47371813977, 47378474074, 30129980512, 46866207841, 42907907171, 47136633957, 47137641574, 47702682727, 46658356332, 46968605808, 40476122229, 47462098039, 41933606011, 47123291259, 47692614781, 31365809276, 46963997819, 43440725120, 47140781186, 47702604931, 47378003089, 47701939347, 47515034773, 26108871838, 42903510176, 43588252841, 46681543856, 47137062065, 46968605877, 47692614839, 46968605881, 26898988218, 44454874298, 47137641661, 47691824319, 47700784324, 47698900186, 47516615901, 47098528993, 17496538343, 35783398645, 46971985144, 47698945274, 46512039163, 47516517627, 47643737341, 37592519938, 47699139843, 47702402309, 45952640263, 47120241942, 17335897369, 45944798490, 21857168674, 47642027303, 47383754024, 47699643692, 42200130860, 30624451894, 29620208961, 42976292164, 44030987589, 46782635334, 47127434576, 47702140243, 45952640342, 47377556826, 47137336667, 47378380122, 33827478877, 46657432925, 45912454492, 43938477417, 40471645545, 45956132210, 46663863673, 44451788156, 46053563777, 37155259783, 43076842892, 41818144141, 47698064783, 47699039634, 41930571156, 44736011670, 40458712473, 47378662814, 45956228513, 42976292268, 47136689585, 47487741362, 33333462468, 29833002436, 47702683080, 41936266699, 44451440084, 42663044569, 27932347866, 47699191259, 47383678425, 36168756708, 46865993190, 21968092646, 23636727278, 46042715631, 26298772978, 47136605683, 37148886516, 47663918584, 47140883960, 46684960265, 41658447376, 26298773015, 41818144283, 46861387293, 47699537443, 25272192547, 47516517923, 27985204778, 36678061611, 47140884019, 47382222392, 45650042429, 47123123774, 47685881412, 47678199370, 46862949964, 45944131152, 47127434832, 16708240979, 46680698456, 47516616281, 47698064989, 26329087581, 37706061412, 47699904106, 42184670833, 41048401524, 47643055734, 44408505976, 47698677369, 20202438265, 41692391033, 41231971972, 47691490948, 46681546372, 45956087433, 47698065038, 47324845710, 33394568847, 46657814162, 47098635924, 47140740758, 42200131225, 43586670238, 37457639072, 29022785186, 47700825764, 47123220133, 45952249509, 26556571303, 47516561072, 47371226802, 47136579257, 44622415552, 24921509572, 47515035335, 46963998413, 42974834382, 32879525584, 34106646229, 43865657046, 46407610071, 21857785576, 47003320041, 45947475694, 47380766452, 47700096756, 46665400054, 46684438260, 47691575029, 19377499899, 47663918844, 47702955779, 47663918851, 47137091337, 46419171083, 42974869260, 45952249611, 45955532555, 38873763603, 47382667028, 46510003995, 46783321889, 46962675499, 42976290608, 47685664564, 47698065209, 35780417338, 47377846076, 45949005630, 47698065225, 44061744971, 47003320144, 36064296786, 24160354133, 47663918935, 18827226973, 43856963426, 38400966500, 47702683495, 47378222956, 47371030384, 44738185074, 33391437686, 47140636537, 47698677627, 47700193147, 41799366525, 17496340357, 45948448648, 44031764363, 47639795597, 47699539857, 46019741587, 47701004187, 42901569442, 42184671140, 45952249768, 47324884906, 45952249772, 42976290738, 16839743412, 47374450615, 47685474236, 47377356733, 45948503998, 47699390407, 46850576333, 42976290770, 44400842706, 20024260570, 41045792731, 35785506783, 42162659298, 41692391397, 47691763692, 47374948337, 47698065394, 47692070906} <class 'set'>\n"]}], "source": ["import pandas as pd\n", "import requests\n", "import os\n", "import json\n", "import pickle\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "from matplotlib import pyplot as plt\n", "from src.dataset.request import TradeDeskManager\n", "from src.config import AdConfig\n", "_config = AdConfig(config_path='./configs/config-prod-3d.yaml', env_path='.env.prod')\n", "\n", "td = TradeDeskManager(_config)\n", "### v2-trade-desk的项目数据最早从2025-05-17开始\n", "# ad_ids = td.get_unpaused_ad_ids(platform='jrtt', project_id=2)  ### 查看还未暂停的广告 str\n", "# ad_ids = td.get_unpaused_ad_ids(platform='kuaishou', project_id=2)  ### 查看还未暂停的广告 int\n", "# ad_ids = td.get_unpaused_ad_ids(platform='gdt', project_id=2)  ### 查看还未暂停的广告 int\n", "print(len(ad_ids), ad_ids, type(ad_ids))\n", "\n", "# all_df = td.fetch_media_data(platform='jrtt', project_id=2, start_ds='2025-05-17', end_ds='2025-07-02', scope='campaign')\n", "# # all_df = td.fetch_media_data(platform='jrtt', project_id=2, start_ds='2025-05-17', end_ds='2025-05-17', scope='ad')\n", "# print(all_df.shape)\n", "# print(all_df.head(5).to_string(line_width=600, index=False))\n", "# print(f'total_cost: {all_df[\"cost\"].sum()//100}, total_7pay: {all_df[\"attribution_game_in_app_ltv_7days\"].sum()//100}, total_7roi: {all_df[\"attribution_game_in_app_ltv_7days\"].sum() / all_df[\"cost\"].sum() * 100 :.2f}%')\n", "\n", "# ### 统计优化目标和深度转化目标\n", "\n", "\n", "# ### 统计ROI系数的影响\n", "# # 优化目标，深度优化目标，深度优化方式\n", "# df = all_df[['cost', 'attribution_game_in_app_ltv_7days', 'roi_goal', 'optimization_goal', 'deep_optimization_goal', 'deep_optimization_type']]\n", "# min_roi, max_roi, step = 0.01, 0.14, 0.005\n", "# for b in np.arange(min_roi, max_roi, step):\n", "#     df_b = df[(b <= df['roi_goal']) & (df['roi_goal'] < b + step)]\n", "#     print(f\"({b:.3f}, {b + step:.3f}): {df_b['cost'].sum()//100}, {df_b['attribution_game_in_app_ltv_7days'].sum()//100}, {df_b['attribution_game_in_app_ltv_7days'].sum() / df_b['cost'].sum() * 100:.2f}%\")\n", "#     print(df_b[['optimization_goal', 'deep_optimization_goal', 'deep_optimization_type']].value_counts())\n", "#     print('=' * 10)\n", "    \n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.17057956220936826\n"]}], "source": ["total = 196870\n", "total_2 =  2642 + 246 + 110 + 10\n", "stop = 182244\n", "print(total_2 / (total + total_2 - stop))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}