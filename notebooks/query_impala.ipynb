{"cells": [{"cell_type": "code", "execution_count": 26, "id": "262d095e-e017-4fdc-8a50-cb639e0e2ef7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["attribution_day  count\n", "     2025-05-08 218659\n", "     2025-06-09 229435\n", "     2025-04-03 280684\n", "     2025-05-27 197365\n", "     2025-04-17 219562\n", "     2025-04-12 429909\n", "     2025-05-19 199407\n", "     2025-05-16 238131\n", "     2025-05-22 206271\n", "     2025-03-27 219220\n", "     2025-04-06 384127\n", "     2025-04-29 218611\n", "     2025-03-28 268047\n", "     2025-04-09 213046\n", "     2025-04-23 206556\n", "     2025-04-18 259899\n", "     2025-04-26 344427\n", "     2025-05-07 205249\n", "     2025-06-03 212038\n", "     2025-05-02 435439\n", "     2025-05-26 196523\n", "     2025-04-02 224327\n", "     2025-05-11 319796\n", "     2025-05-05 316704\n", "     2025-06-04 210194\n", "     2025-04-21 220588\n", "     2025-05-18 335432\n", "     2025-04-24 214898\n", "     2025-05-28 205761\n", "     2025-05-13 199184\n", "     2025-06-06 245548\n", "     2025-05-20 198461\n", "     2025-04-10 215863\n", "     2025-04-04 482125\n", "     2025-04-08 218649\n", "     2025-03-29 379968\n", "     2025-04-01 224604\n", "     2025-06-10 221106\n", "     2025-04-15 208841\n", "     2025-05-31 458300\n", "     2025-05-25 331078\n", "     2025-05-12 195028\n", "     2025-04-07 247232\n", "     2025-04-28 213155\n", "     2025-06-11 210601\n", "     2025-05-10 353321\n", "     2025-06-02 347981\n", "     2025-04-16 213821\n", "     2025-06-08 347594\n", "     2025-05-09 236965\n", "     2025-04-30 268530\n", "     2025-06-01 418452\n", "     2025-05-14 201705\n", "     2025-04-11 256247\n", "     2025-04-22 213535\n", "     2025-05-29 223289\n", "     2025-04-19 418730\n", "     2025-06-07 371138\n", "     2025-04-13 349718\n", "     2025-05-23 234752\n", "     2025-04-05 427184\n", "     2025-03-31 230813\n", "     2025-05-30 265509\n", "     2025-05-03 394940\n", "     2025-03-26 209956\n", "     2025-05-15 205664\n", "     2025-05-21 200967\n", "     2025-05-17 374310\n", "     2025-04-27 216253\n", "     2025-04-14 212658\n", "     2025-05-06 203608\n", "     2025-06-05 211665\n", "     2025-05-04 362249\n", "     2025-04-20 365699\n", "     2025-04-25 238548\n", "     2025-05-01 460940\n", "     2025-03-30 337759\n", "     2025-05-24 366289\n", "(124, 5)\n", "total packages: 11\n", "new accounts, ours: 2\n", "new accounts, others: 3\n", "old accounts, ours: 13\n", "old accounts, others: 106\n", "channel_ty_account_id                  sdk_package_name adtrace_platform min_attribution_day account_type\n", "     ****************        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     **************** com.tuyoo.fish3d.jinritoutiao.jsb             jrtt          2025-05-14       others\n", "     ****************                                               jrtt          2025-05-14       others\n", "     ****************  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     ****************     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     ****************        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     ****************  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     ****************                                               jrtt          2025-05-14       others\n", "     ****************                                               jrtt          2025-05-14       others\n", "     1810972500949147                                               jrtt          2025-05-14       others\n", "     **************** com.tuyoo.fish3d.jinritoutiao.gbb             jrtt          2025-05-14         ours\n", "     1829643853725700        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1771815366490120  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     1817948045672452 com.tuyoo.fish3d.jinritoutiao.jdb             jrtt          2025-05-28       others\n", "     1817948045672452 com.tuyoo.fish3d.jinritoutiao.flb             jrtt          2025-05-14       others\n", "     1681770643803143  com.tuyoo.fish3d.jinritoutiaolm3             jrtt          2025-05-20       others\n", "     1827469447521290                                               jrtt          2025-05-14       others\n", "     1771815367135310  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     1810972499865833                                               jrtt          2025-05-14       others\n", "     1817948042466644 com.tuyoo.fish3d.jinritoutiao.gbb             jrtt          2025-05-14       others\n", "     1683578377626638                                               jrtt          2025-05-14       others\n", "     1810972231942307 com.tuyoo.fish3d.jinritoutiao.gbb             jrtt          2025-05-14       others\n", "     1824475485397338                                               jrtt          2025-05-14       others\n", "     1759245760564238 com.tuyoo.fish3d.jinritoutiao.flb             jrtt          2025-05-14       others\n", "     **************** com.tuyoo.fish3d.jinritoutiao.gbb             jrtt          2025-05-14         ours\n", "     ****************     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14         ours\n", "     1810972502699051                                               jrtt          2025-05-14       others\n", "     1778175543300173     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1681778093388807                                               jrtt          2025-05-14       others\n", "     1766941777175565        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1810972257830922  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     1810972504859659                                               jrtt          2025-05-14       others\n", "     1827469445260361                                               jrtt          2025-05-14       others\n", "     ****************        com.tuyoo.fish3d.official4             jrtt          2025-05-14         ours\n", "     1824543667409924        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1807890153273483                                               jrtt          2025-05-14       others\n", "     1824474729701786        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1824543665396825     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1683578313782285  com.tuyoo.fish3d.jinritoutiaolm3             jrtt          2025-05-20       others\n", "     1807890147245193                                               jrtt          2025-05-14       others\n", "     1810972214316035        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1817948032105539     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1810972257212436 com.tuyoo.fish3d.jinritoutiao.jsb             jrtt          2025-05-14       others\n", "     1824474728067604        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1810972841495568  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     1810972265723220  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     1824474730472644        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1782410724011019        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1824474734942211        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1810972266799180  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     1771815324966919     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1827469504224300        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1741934487776327     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1817948031307988     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1829643961621403 com.tuyoo.fish3d.jinritoutiao.flb             jrtt          2025-05-14       others\n", "     1827469496526851        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1827469446998155                                               jrtt          2025-05-14       others\n", "     1829643849151555 com.tuyoo.fish3d.jinritoutiao.jsb             jrtt          2025-05-14       others\n", "     1827469445882955                                               jrtt          2025-05-14       others\n", "     1771815369031687     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1824474724720771     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1807808453754889  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     ****************     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14         ours\n", "     1827469496043210        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     ****************     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14         ours\n", "     **************** com.tuyoo.fish3d.jinritoutiao.flb             jrtt          2025-05-14         ours\n", "     1829643852971011  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     ****************        com.tuyoo.fish3d.official4             jrtt          2025-05-14         ours\n", "     1681771606094855 com.tuyoo.fish3d.jinritoutiao.flb             jrtt          2025-05-14       others\n", "     ****************     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14         ours\n", "     1810972256634890 com.tuyoo.fish3d.jinritoutiao.jsb             jrtt          2025-05-14       others\n", "     1829643968090308     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     **************** com.tuyoo.fish3d.jinritoutiao.flb             jrtt          2025-05-14         ours\n", "     1824474723167603     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1817948044860448 com.tuyoo.fish3d.jinritoutiao.flb             jrtt          2025-05-14       others\n", "     1810972503714107                                               jrtt          2025-05-14       others\n", "     1824475484607203                                               jrtt          2025-05-14       others\n", "     1705980039088141  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     1827469444660361                                               jrtt          2025-05-14       others\n", "     1771815365900296 com.tuyoo.fish3d.jinritoutiaopos1             jrtt          2025-05-14       others\n", "     1752971773854728                                               jrtt          2025-05-14       others\n", "     1807890147879002                                               jrtt          2025-05-14       others\n", "     1827469501024396 com.tuyoo.fish3d.jinritoutiao.jsb             jrtt          2025-05-14       others\n", "     1810972258515988 com.tuyoo.fish3d.jinritoutiao.gbb             jrtt          2025-05-14       others\n", "     1810972206994547        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1810972264699132  com.tuyoo.fish3d.jinritoutiaolm2             jrtt          2025-05-14       others\n", "     1740123122968647                                               jrtt          2025-05-14       others\n", "     1681778093885454                                               jrtt          2025-05-14       others\n", "     1827469495368714        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1824475483706788                                               jrtt          2025-05-14       others\n", "     1745015947166733     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     ****************     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14         ours\n", "     1829643850683788 com.tuyoo.fish3d.jinritoutiao.flb             jrtt          2025-05-14       others\n", "     1817948044860448 com.tuyoo.fish3d.jinritoutiao.jdb             jrtt          2025-05-28       others\n", "     1771815327524872 com.tuyoo.fish3d.jinritoutiaopos1             jrtt          2025-05-14       others\n", "     1810972499325193                                               jrtt          2025-05-14       others\n", "     **************** com.tuyoo.fish3d.jinritoutiao.jdb             jrtt          2025-05-28         ours\n", "     1766941992339479                                               jrtt          2025-05-14       others\n", "     1807890153971866                                               jrtt          2025-05-14       others\n", "     1759245822253064 com.tuyoo.fish3d.jinritoutiao.jdb             jrtt          2025-05-28       others\n", "     1740123118267400                                               jrtt          2025-05-14       others\n", "     1827469446464201                                               jrtt          2025-05-14       others\n", "     1824474728857612 com.tuyoo.fish3d.jinritoutiao.gbb             jrtt          2025-05-14       others\n", "     1810972253154675        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     **************** com.tuyoo.fish3d.jinritoutiao.jdb             jrtt          2025-05-28         ours\n", "     1810972254712841        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1824474723952964     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1829643848369667 com.tuyoo.fish3d.jinritoutiao.jsb             jrtt          2025-05-14       others\n", "     1824543669938314 com.tuyoo.fish3d.jinritoutiao.gbb             jrtt          2025-05-14       others\n", "     1771815325527191 com.tuyoo.fish3d.jinritoutiaopos1             jrtt          2025-05-14       others\n", "     1829643851445835        com.tuyoo.fish3d.official4             jrtt          2025-05-14       others\n", "     1810972215163035        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1771815369606152     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     **************** com.tuyoo.fish3d.jinritoutiao.gbb             jrtt          2025-05-14         ours\n", "     1827469501527049 com.tuyoo.fish3d.jinritoutiao.jsb             jrtt          2025-05-14       others\n", "     1824474726398075     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     ****************        com.tuyoo.fish3d.official5             jrtt          2025-05-14         ours\n", "     1829643854643524        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1827469503665755        com.tuyoo.fish3d.official5             jrtt          2025-05-14       others\n", "     1771815370197000     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1827469500400650     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1810972500407497                                               jrtt          2025-05-14       others\n", "     1817948027236644     com.tuyoo.fish3d.jinritoutiao             jrtt          2025-05-14       others\n", "     1766942004613127                                               jrtt          2025-05-14       others\n"]}], "source": ["import pymysql\n", "import pickle\n", "import pandas as pd\n", "from impala.dbapi import connect\n", "\n", "def load_td_data(filepath, start_ds=None, end_ds=None):\n", "    with open(filepath, 'rb') as f:\n", "        new_fetched_data = pickle.load(f)\n", "    if start_ds is None or end_ds is None:\n", "        return new_fetched_data\n", "    return {ds: new_fetched_data[ds] for ds in pd.date_range(start=start_ds, end=end_ds)}\n", "\n", "# td_data = load_td_data(f'../data/td_data_2025-01-01_2025-05-06.pkl')\n", "\n", "\n", "def query_ad_impala(start_ds, end_ds):\n", "    accounts_1 = [\"****************\",\"****************\",\"****************\",\"****************\",\"****************\",\"****************\",\"****************\",\"****************\",\"****************\",\"****************\",\"****************\"]\n", "    accounts_2 = [\"****************\",\"****************\",\"****************\",\"****************\"]\n", "    conn = connect(\n", "        host=\"**********\",\n", "        port=5006,\n", "        auth_mechanism=\"NOSASL\"\n", "    )\n", "    with conn.cursor(user=\"koi_user\") as cursor:    \n", "        cursor.execute(\"refresh koi_data.dwd_advertisement_feature_20461\")\n", "        query = \"\"\"\n", "        select distinct channel_ty_account_id, sdk_package_name, adtrace_platform\n", "        from koi_data.dwd_advertisement_feature_20461 \n", "        where attribution_day between '{start_ds}' and '{end_ds}' \n", "            and channel_ty_account_id is not null \n", "            and cast(adtrace_aid as int) in (2, 21, 87, 82, 78)\n", "            and adtrace_platform in ('jrtt', 'jrtt_wxgame', 'jrtt_xiansuo_v3', 'jrtt_xiansuo_v2', 'jrtt_dygame')\n", "        \"\"\".format(start_ds=start_ds, end_ds=end_ds)\n", "        cursor.execute(query)\n", "        result = cursor.fetchall()\n", "        \n", "        query_2 = \"\"\"\n", "        select sdk_package_name, min(attribution_day) as min_attribution_day\n", "        from koi_data.dwd_advertisement_feature_20461 \n", "        where attribution_day between '{start_ds}' and '{end_ds}' \n", "            and channel_ty_account_id is not null \n", "            and cast(adtrace_aid as int) in (2, 21, 87, 82, 78)\n", "            and adtrace_platform in ('jrtt', 'jrtt_wxgame', 'jrtt_xiansuo_v3', 'jrtt_xiansuo_v2', 'jrtt_dygame')\n", "        group by sdk_package_name\n", "        \"\"\".format(start_ds=start_ds, end_ds=end_ds)\n", "        cursor.execute(query_2)\n", "        result_2 = cursor.fetchall()\n", "        \n", "        query_3 = \"\"\"\n", "        select attribution_day, count(*) from koi_data.dwd_advertisement_feature_20461 group by attribution_day\n", "        \"\"\"\n", "        cursor.execute(query_3)\n", "        result_3 = cursor.fetchall()\n", "        \n", "        \n", "        \n", "    conn.close()\n", "    result_df = pd.DataFrame(result)\n", "    result_df.columns = ['channel_ty_account_id', 'sdk_package_name', 'adtrace_platform']\n", "    result_df_2 = pd.DataFrame(result_2)\n", "    result_df_2.columns = ['sdk_package_name', 'min_attribution_day']\n", "    result_df = result_df.merge(result_df_2, on='sdk_package_name', how='left')\n", "    result_df['account_type'] = result_df['channel_ty_account_id'].apply(lambda x: 'ours' if x in accounts_1 + accounts_2 else 'others')\n", "    \n", "    result_df_3 = pd.DataFrame(result_3)\n", "    result_df_3.columns = ['attribution_day', 'count']\n", "    print(result_df_3.to_string(line_width=200, index=False))\n", "    return result_df\n", "\n", "result_df = query_ad_impala(start_ds='2025-05-14', end_ds='2025-06-04')\n", "print(result_df.shape)\n", "print(f\"total packages: {result_df['sdk_package_name'].nunique()}\")\n", "print(f\"new accounts, ours: {result_df[(result_df['account_type'] == 'ours') & (result_df['min_attribution_day'] >= '2025-05-28')].shape[0]}\")\n", "print(f\"new accounts, others: {result_df[(result_df['account_type'] == 'others') & (result_df['min_attribution_day'] >= '2025-05-28')].shape[0]}\")\n", "print(f\"old accounts, ours: {result_df[(result_df['account_type'] == 'ours') & (result_df['min_attribution_day'] < '2025-05-28')].shape[0]}\")\n", "print(f\"old accounts, others: {result_df[(result_df['account_type'] == 'others') & (result_df['min_attribution_day'] < '2025-05-28')].shape[0]}\")\n", "print(result_df.to_string(line_width=200, index=False))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 45, "id": "f2a9c12e-12f9-4767-8067-c6cf7a3ed8c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO: Pandarallel will run on 4 workers.\n", "INFO: Pandarallel will use standard multiprocessing data transfer (pipe) to transfer data between the main process and workers.\n", "   ad_id         dt   cost   pay7 category\n", "0      A 2024-02-12  68.39  55.00        X\n", "1      A 2024-02-13  49.74   5.00        Z\n", "2      A 2024-02-14  38.35  10.00        Z\n", "3      A 2024-02-15   0.00   0.00      NaN\n", "4      A 2024-02-16   0.00   0.00      NaN\n", "5      A 2024-02-17  29.20  97.00        Y\n", "6      B 2024-01-12  79.17  63.00        Y\n", "7      B 2024-01-13   0.00   0.00      NaN\n", "8      B 2024-01-14   0.00   0.00      NaN\n", "9      B 2024-01-15  44.18  94.00        Z\n", "10     B 2024-01-16  43.02  31.00        X\n", "11     B 2024-01-17   0.00   0.00      NaN\n", "12     B 2024-01-18  22.12  88.00        Y\n", "13     C 2024-02-20  19.59  22.00        Y\n", "14     C 2024-02-21  87.00  34.00        X\n", "15     C 2024-02-22   0.00   0.00      NaN\n", "16     C 2024-02-23  61.18  84.00        Z\n", "17     C 2024-02-24   0.00   0.00      NaN\n", "18     C 2024-02-25  52.89  53.00        X\n", "19     D 2024-01-21  14.29  67.00        Y\n", "20     D 2024-01-22   0.00   0.00      NaN\n", "21     D 2024-01-23   0.00   0.00      NaN\n", "22     D 2024-01-24  95.33  62.00        X\n", "23     D 2024-01-25   0.00   0.00      NaN\n", "24     D 2024-01-26   0.00   0.00      NaN\n", "25     D 2024-01-27  47.36  12.00        Z\n", "26     D 2024-01-28   0.00   0.00      NaN\n", "27     D 2024-01-29  56.80  46.00        Z\n", "28     E 2024-02-10  71.59  27.00        Z\n", "29     E 2024-02-11   0.00   0.00      NaN\n", "30     E 2024-02-12   0.00   0.00      NaN\n", "31     E 2024-02-13   0.00   0.00      NaN\n", "32     E 2024-02-14   0.00   0.00      NaN\n", "33     E 2024-02-15   0.00   0.00      NaN\n", "34     E 2024-02-16   0.00   0.00      NaN\n", "35     E 2024-02-17  94.37  70.00        Y\n", "36     E 2024-02-18  96.37  41.00        X\n", "37     E 2024-02-19  92.56  14.00        Y\n", "38     F 2024-02-26  10.12  11.12        X\n", "   ad_id         dt   cost   pay7 category  future_cost_sum  future_pay7_sum  future_roi\n", "0      A 2024-02-12  68.39  55.00        X           117.29           167.00    1.423821\n", "1      A 2024-02-13  49.74   5.00        Z            67.55           112.00    1.658031\n", "2      A 2024-02-14  38.35  10.00        Z            29.20           107.00    3.664384\n", "3      A 2024-02-15   0.00   0.00      NaN            29.20            97.00    3.321918\n", "4      A 2024-02-16   0.00   0.00      NaN            29.20            97.00    3.321918\n", "5      A 2024-02-17  29.20  97.00        Y              NaN            97.00         NaN\n", "6      B 2024-01-12  79.17  63.00        Y           109.32           276.00    2.524698\n", "7      B 2024-01-13   0.00   0.00      NaN           109.32           213.00    1.948408\n", "8      B 2024-01-14   0.00   0.00      NaN           109.32           213.00    1.948408\n", "9      B 2024-01-15  44.18  94.00        Z            65.14           213.00    3.269880\n", "10     B 2024-01-16  43.02  31.00        X            22.12           119.00    5.379747\n", "11     B 2024-01-17   0.00   0.00      NaN            22.12            88.00    3.978300\n", "12     B 2024-01-18  22.12  88.00        Y              NaN            88.00         NaN\n", "13     C 2024-02-20  19.59  22.00        Y           201.07           193.00    0.959865\n", "14     C 2024-02-21  87.00  34.00        X           114.07           171.00    1.499080\n", "15     C 2024-02-22   0.00   0.00      NaN           114.07           137.00    1.201017\n", "16     C 2024-02-23  61.18  84.00        Z            52.89           137.00    2.590282\n", "17     C 2024-02-24   0.00   0.00      NaN            52.89            53.00    1.002080\n", "18     C 2024-02-25  52.89  53.00        X              NaN            53.00         NaN\n", "19     D 2024-01-21  14.29  67.00        Y           142.69           141.00    0.988156\n", "20     D 2024-01-22   0.00   0.00      NaN           199.49            74.00    0.370946\n", "21     D 2024-01-23   0.00   0.00      NaN           199.49           120.00    0.601534\n", "22     D 2024-01-24  95.33  62.00        X           104.16           120.00    1.152074\n", "23     D 2024-01-25   0.00   0.00      NaN           104.16            58.00    0.556836\n", "24     D 2024-01-26   0.00   0.00      NaN           104.16            58.00    0.556836\n", "25     D 2024-01-27  47.36  12.00        Z            56.80            58.00    1.021127\n", "26     D 2024-01-28   0.00   0.00      NaN            56.80            46.00    0.809859\n", "27     D 2024-01-29  56.80  46.00        Z              NaN            46.00         NaN\n", "28     E 2024-02-10  71.59  27.00        Z            94.37            27.00    0.286108\n", "29     E 2024-02-11   0.00   0.00      NaN           190.74            70.00    0.366992\n", "30     E 2024-02-12   0.00   0.00      NaN           283.30           111.00    0.391811\n", "31     E 2024-02-13   0.00   0.00      NaN           283.30           125.00    0.441228\n", "32     E 2024-02-14   0.00   0.00      NaN           283.30           125.00    0.441228\n", "33     E 2024-02-15   0.00   0.00      NaN           283.30           125.00    0.441228\n", "34     E 2024-02-16   0.00   0.00      NaN           283.30           125.00    0.441228\n", "35     E 2024-02-17  94.37  70.00        Y           188.93           125.00    0.661621\n", "36     E 2024-02-18  96.37  41.00        X            92.56            55.00    0.594209\n", "37     E 2024-02-19  92.56  14.00        Y              NaN            14.00         NaN\n", "38     F 2024-02-26  10.12  11.12        X              NaN            11.12         NaN\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from pandarallel import pandarallel\n", "pandarallel.initialize()\n", "\n", "def _fill_missing_dates(group):\n", "    min_date, max_date = group['dt'].min(), group['dt'].max()\n", "    date_range = pd.date_range(start=min_date, end=max_date, name='dt')\n", "    return (\n", "        group.set_index('dt')\n", "        .reindex(date_range)\n", "        .fillna(0)\n", "        .reset_index()\n", "    )\n", "    \n", "    \n", "def generate_label_fast(df, label_range=7):\n", "    \"\"\"\n", "    对整个 DataFrame 向量化地生成 future_cost_sum 和 future_pay7_sum，\n", "    并避免使用 groupby().apply()\n", "    \"\"\"\n", "    # 确保按 ad_id 和 dt 排序, 按照 dt 倒序\n", "    df = df.sort_values(['ad_id', 'dt'], ascending=[True, False])\n", "    df['future_cost_sum'] = (\n", "        df.groupby('ad_id')['cost']\n", "        .rolling(window=label_range, min_periods=1)\n", "        .sum()\n", "        .reset_index(level=0, drop=True)\n", "    )\n", "    df['future_pay7_sum'] = (\n", "        df.groupby('ad_id')['pay7']\n", "        .rolling(window=label_range, min_periods=1)\n", "        .sum()\n", "        .reset_index(level=0, drop=True)\n", "    )\n", "    df['future_cost_sum'] = df.groupby('ad_id')['future_cost_sum'].shift(1)\n", "    df['future_pay7_sum'] = df.groupby('ad_id')['future_pay7_sum'].shift(1)\n", "    df['future_roi'] = df['future_pay7_sum'] / (df['future_cost_sum'] + 1e-9)\n", "    # 恢复为正序\n", "    df = df.sort_values(['ad_id', 'dt']).reset_index(drop=True)\n", "\n", "    return df\n", "\n", "    \n", "def _fill_missing_dates_per_group(df, id_col='ad_id', date_col='dt'):\n", "    \"\"\"\n", "    为每个 ad_id 根据其自身的起止日期补全中间缺失的日期\n", "    并保留原始特征列，数值型填充0，非数值型填充NaN\n", "    \"\"\"\n", "    df[date_col] = pd.to_datetime(df[date_col])\n", "    group_bounds = (\n", "        df.groupby(id_col)[date_col]\n", "        .agg(['min', 'max'])\n", "        .rename(columns={'min': 'start_date', 'max': 'end_date'})\n", "        .reset_index()\n", "    )\n", "    \n", "    def _generate_date_ranges(row):\n", "        return pd.date_range(row['start_date'], row['end_date'], name=date_col)\n", "\n", "    group_bounds['dt'] = group_bounds.apply(_generate_date_ranges, axis=1)\n", "    group_bounds = group_bounds.explode('dt')[[id_col, date_col]]\n", "    \n", "    filled_df = pd.merge(\n", "        group_bounds,\n", "        df,\n", "        on=[id_col, date_col],\n", "        how='left'\n", "    )\n", "    numeric_cols = df.select_dtypes(include=np.number).columns\n", "    non_numeric_cols = df.columns.difference(numeric_cols).difference([id_col, date_col])\n", "\n", "    filled_df[numeric_cols] = filled_df[numeric_cols].fillna(0)\n", "    filled_df[non_numeric_cols] = filled_df[non_numeric_cols].fillna(np.nan)\n", "\n", "    return filled_df\n", "\n", "data = {\n", "    'ad_id': ['A', 'B', 'C', 'D', 'E', \n", "              'A', 'B', 'C', 'D', 'E',\n", "              'A', 'B', 'C', 'D', 'E', \n", "              'A', 'B', 'C', 'D', 'E', 'F'],\n", "    'dt': ['2024-02-13', '2024-01-18', '2024-02-21', '2024-01-27', '2024-02-17',\n", "           '2024-02-12', '2024-01-15', '2024-02-20', '2024-01-24', '2024-02-10',\n", "           '2024-02-17', '2024-01-16', '2024-02-23', '2024-01-21', '2024-02-18',\n", "           '2024-02-14', '2024-01-12', '2024-02-25', '2024-01-29', '2024-02-19', '2024-02-26'],\n", "    'cost': [49.74, 22.12, 87.00, 47.36, 94.37,\n", "                68.39, 44.18, 19.59, 95.33, 71.59,\n", "                29.20, 43.02, 61.18, 14.29, 96.37,\n", "                38.35, 79.17, 52.89, 56.80, 92.56, 10.12],\n", "    'pay7': [5, 88, 34, 12, 70,\n", "                55, 94, 22, 62, 27,\n", "                97, 31, 84, 67, 41,\n", "                10, 63, 53, 46, 14, 11.12],\n", "    'category': ['Z', 'Y', 'X', 'Z', 'Y',\n", "                 'X', 'Z', 'Y', 'X', 'Z',\n", "                 'Y', 'X', 'Z', 'Y', 'X',\n", "                 'Z', 'Y', 'X', 'Z', 'Y', 'X']\n", "}\n", "x = pd.DataFrame(data)\n", "x['dt'] = pd.to_datetime(x['dt'])\n", "y = x.groupby('ad_id').parallel_apply(_fill_missing_dates).reset_index(drop=True)\n", "# x = x.groupby('ad_id').parallel_apply(_fill_missing_dates_per_group)\n", "# print(y)\n", "# print('**' * 5)\n", "x = _fill_missing_dates_per_group(x)\n", "print(x.to_string())\n", "x = generate_label_fast(x)\n", "print(x.to_string())\n", "\n"]}, {"cell_type": "code", "execution_count": 47, "id": "5280589e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.7495431943162016\n", "4.890005629029408\n", "5.115402510710955\n", "5.8137204490584535\n"]}], "source": ["x = [(328216, 2.08, 4.36, 4.59, 5.90), (265598, 1.91, 6.87, 6.83, 5.22), (260685, 1.17, 3.54, 4.03, 6.31)]\n", "for i in range(1, 5):\n", "    y= sum([x[j][0] *x[j][i] for j in range(0, 3)]) / sum([x[j][0] for j in range(0,3)])\n", "    print(y)\n", "    "]}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}