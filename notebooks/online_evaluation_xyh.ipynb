{"cells": [{"cell_type": "code", "execution_count": 35, "id": "8c8e4879-0d87-4f50-86cc-e725e08186f1", "metadata": {}, "outputs": [], "source": ["# global_td_data = __fetched"]}, {"cell_type": "code", "execution_count": 1, "id": "5184f26f-efeb-48bb-b806-d14cf21b2339", "metadata": {}, "outputs": [], "source": ["import io\n", "import time\n", "import json\n", "import traceback\n", "import pytz\n", "import requests\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "\n", "\n", "def fetch_data_download(date_start, date_end, column_list, max_retries):\n", "    url = \"https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/report/v1/platform/group/download/\"\n", "    headers = {\n", "        # 'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjozMjUsInVzZXJuYW1lIjoiemhvdXhpYW9xaXUiLCJpc3MiOiJhZC1tYW5hZ2VyIiwiZXhwIjoxNzQ1ODk3NDgzLCJuYmYiOjE3NDUyOTI2ODMsImlhdCI6MTc0NTI5MjY4M30.fTrS5uIF90nSeStlRiTi4ibXOj5b32QtLq5hbK-qT6c',\n", "        # 'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjozMjUsInVzZXJuYW1lIjoiemhvdXhpYW9xaXUiLCJpc3MiOiJhZC1tYW5hZ2VyIiwiZXhwIjoxNzQ2NTE2NjgxLCJuYmYiOjE3NDU5MTE4ODEsImlhdCI6MTc0NTkxMTg4MX0.kyEjp3pCrGtbnT_GtZ1l-EZtNEVJzqkXDu3Z51-wyVg',\n", "        'Authorization': \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjozMjUsInVzZXJuYW1lIjoiemhvdXhpYW9xaXUiLCJpc3MiOiJhZC1tYW5hZ2VyIiwiZXhwIjoxNzQ3MTM0NTcyLCJuYmYiOjE3NDY1Mjk3NzIsImlhdCI6MTc0NjUyOTc3Mn0.cZHAli15GgFtVIQfOFlfZIcM8iBIGzRGdQRuF6Ftyz4\",\n", "        'Content-Type': 'application/json'\n", "    }\n", "    \n", "    delta_day = (date_end - date_start).days\n", "    date_start = date_start.strftime('%Y-%m-%d')\n", "    date_end = date_end.strftime('%Y-%m-%d')\n", "    date_update = datetime.now().date().strftime('%Y-%m-%d')\n", "\n", "    payload = json.dumps({\n", "        \"page\": 1,\n", "        \"page_size\": 20,\n", "        \"start_date\": date_start,\n", "        \"end_date\": date_end,\n", "        \"order_by\": \"ad_id\",\n", "        \"order_type\": \"desc\",\n", "        \"dimension\": 9,\n", "        \"income_type\": 2,\n", "        \"platform\": [\"1\"],\n", "        \"project\": [\"2\"],\n", "        \"column_list\": column_list,\n", "        \"dimension_list\": [9, 11]  # 账户ID, 广告ID\n", "    })\n", "\n", "    try:\n", "        if delta_day < 2:\n", "            response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "            response.raise_for_status()\n", "        else:\n", "            post_response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "            post_response.raise_for_status()\n", "\n", "            mission_id = post_response.json()['data']['id']\n", "            mission_url = \"https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/async/v1/tasks/{}/\".format(mission_id)\n", "\n", "            data = None\n", "            retries = 0\n", "            while not data:\n", "                if retries >= max_retries:\n", "                    raise TimeoutError(f\"Data fetch timeout after {max_retries} attempts.\")\n", "\n", "                data = requests.request(\"GET\", mission_url, headers=headers).json()['data']\n", "                time.sleep(1)\n", "\n", "            download_url = data['url']\n", "            download_headers = {\n", "                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0'\n", "            }\n", "            response = requests.request(\"GET\", download_url, headers=download_headers, timeout=30)\n", "\n", "        bytes_io = io.BytesIO(response.content)\n", "        df_columns = ['account_id', 'ad_id'] + column_list\n", "        df = pd.read_excel(bytes_io)\n", "        df.columns = df_columns\n", "        df = df[(df['ad_id'] != 0) & (df['ad_id'] != '未知')]\n", "        df['date_start'] = date_start\n", "        df['date_end'] = date_end\n", "        df['date_update'] = date_update\n", "        \n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"HTTP request failed: {e}\")\n", "        print(traceback.format_exc())\n", "        return 1\n", "\n", "    except TimeoutError as e:\n", "        print(f\"Timeout error: {e}\")\n", "        print(traceback.format_exc())\n", "        return 1\n", "\n", "    except Exception as e:\n", "        print(f\"Unexpected error: {e}\")\n", "        print(traceback.format_exc())\n", "        return 1\n", "\n", "    return df\n"]}, {"cell_type": "code", "execution_count": 36, "id": "fdd84c85-9b79-424b-87ba-81e777cd39b2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def fetch_all_data(start_ds, end_ds):\n", "    global global_td_data\n", "    # column_list=[\"cost\",\"new_user\",\"pay1\",\"pay2\",\"pay3\",\"pay4\",\"pay5\",\"pay6\",\"pay7\",\"pay8\",\"pay9\",\"pay10\",\"pay11\",\"pay12\",\"pay13\",\"pay14\",\"pay15\",\"pay30\",\"pay45\",\"pay60\",\"pay90\",\"pay120\",\"pay150\",\"pay180\",\"pay210\",\"pay240\",\"pay270\",\"pay300\",\"pay360\",\"total_pay\"]\n", "    feature_columns = [\"cost\", \"show\",\"click\",\"cost_by_thousand_show\",\"new_user\",\"new_paid_user\",\"new_user_cost\",\"new_paid_user_cost\",\n", "                       \"ltv1\",\"roi1\",\"cvr\",\"ctr\",\"convert\",\"active_cost\",\"convert_cost\",\"active\",\"ad_id_total\",\n", "                       \"pay1\",\"pay2\",\"pay3\",\"pay4\",\"pay5\",\"pay6\",\"pay7\",\"pay15\",\"pay30\",\"pay180\",\n", "                       \"ltv2\",\"ltv3\",\"ltv4\",\"ltv5\",\"ltv6\",\"ltv7\",\"ltv15\",\"ltv30\",\"ltv180\",\n", "                       \"roi2\",\"roi3\",\"roi4\",\"roi5\",\"roi6\",\"roi7\",\"roi15\",\"roi30\",\"roi90\",\"roi180\",\n", "                       \"stay_num2\",\"stay_num3\",\"stay_num4\",\"stay_num5\",\"stay_num6\",\"stay_num7\",\"stay_num15\",\"stay_num30\",\"stay_num90\",\"stay_num180\",\n", "                       \"ltv90\",\"pay90\",\"new_user_ad_trace\",\"pay1_ad_trace\",\"pay7_ad_trace\", \"total_pay\"]\n", "    tmp_data = {}\n", "    for ds in pd.date_range(start_ds, end_ds):\n", "        if ds not in global_td_data:\n", "            df = fetch_data_download(ds, ds, feature_columns, max_retries=10)\n", "            tmp_data[ds] = df\n", "            print(f'ds: {ds} downloaded')\n", "            global_td_data[ds] = df\n", "        else:\n", "            tmp_data[ds] = global_td_data[ds]\n", "    return tmp_data\n", "\n", "        \n", "def fetch_all_result(start_ds, end_ds):\n", "    ds = '2025-04-16'\n", "    result_df = query_intervention_result(ds)\n", "    print(result_df)\n", "\n", "# start_ds, end_ds = '2025-05-01', '2025-05-05'\n", "# # fetch_all_data(start_ds='2024-01-01', end_ds='2024-09-30')\n", "# new_fetched_data = fetch_all_data(start_ds=start_ds, end_ds=end_ds)\n", "# import pickle\n", "# with open(f'../data/td_data_{start_ds}_{end_ds}.pkl', 'wb') as f:\n", "#     pickle.dump(new_fetched_data, f)"]}, {"cell_type": "code", "execution_count": 41, "id": "e89bcf5e-2721-401f-80b5-ebef42cf16e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ds: 2024-09-21 00:00:00 downloaded\n", "ds: 2024-09-22 00:00:00 downloaded\n", "ds: 2024-09-23 00:00:00 downloaded\n", "ds: 2024-09-24 00:00:00 downloaded\n", "ds: 2024-09-25 00:00:00 downloaded\n", "ds: 2024-09-26 00:00:00 downloaded\n", "ds: 2024-09-27 00:00:00 downloaded\n", "ds: 2024-09-28 00:00:00 downloaded\n", "ds: 2024-09-29 00:00:00 downloaded\n", "ds: 2024-09-30 00:00:00 downloaded\n", "ds: 2024-10-01 00:00:00 downloaded\n", "ds: 2024-10-02 00:00:00 downloaded\n", "ds: 2024-10-03 00:00:00 downloaded\n", "ds: 2024-10-04 00:00:00 downloaded\n", "ds: 2024-10-05 00:00:00 downloaded\n", "ds: 2024-10-06 00:00:00 downloaded\n", "ds: 2024-10-07 00:00:00 downloaded\n", "ds: 2024-10-08 00:00:00 downloaded\n", "ds: 2024-10-09 00:00:00 downloaded\n", "ds: 2024-10-10 00:00:00 downloaded\n", "ds: 2024-10-11 00:00:00 downloaded\n", "ds: 2024-10-12 00:00:00 downloaded\n", "ds: 2024-10-13 00:00:00 downloaded\n", "ds: 2024-10-14 00:00:00 downloaded\n", "ds: 2024-10-15 00:00:00 downloaded\n", "ds: 2024-10-16 00:00:00 downloaded\n", "ds: 2024-10-17 00:00:00 downloaded\n", "ds: 2024-10-18 00:00:00 downloaded\n", "ds: 2024-10-19 00:00:00 downloaded\n", "ds: 2024-10-20 00:00:00 downloaded\n", "ds: 2024-10-21 00:00:00 downloaded\n", "ds: 2024-10-22 00:00:00 downloaded\n", "ds: 2024-10-23 00:00:00 downloaded\n", "ds: 2024-10-24 00:00:00 downloaded\n", "ds: 2024-10-25 00:00:00 downloaded\n", "ds: 2024-10-26 00:00:00 downloaded\n", "ds: 2024-10-27 00:00:00 downloaded\n", "ds: 2024-10-28 00:00:00 downloaded\n", "ds: 2024-10-29 00:00:00 downloaded\n", "ds: 2024-10-30 00:00:00 downloaded\n", "ds: 2024-10-31 00:00:00 downloaded\n", "ds: 2024-11-01 00:00:00 downloaded\n", "ds: 2024-11-02 00:00:00 downloaded\n", "ds: 2024-11-03 00:00:00 downloaded\n", "ds: 2024-11-04 00:00:00 downloaded\n", "ds: 2024-11-05 00:00:00 downloaded\n", "ds: 2024-11-06 00:00:00 downloaded\n", "ds: 2024-11-07 00:00:00 downloaded\n", "ds: 2024-11-08 00:00:00 downloaded\n", "ds: 2024-11-09 00:00:00 downloaded\n", "ds: 2024-11-10 00:00:00 downloaded\n", "ds: 2024-11-11 00:00:00 downloaded\n", "ds: 2024-11-12 00:00:00 downloaded\n", "ds: 2024-11-13 00:00:00 downloaded\n", "ds: 2024-11-14 00:00:00 downloaded\n", "ds: 2024-11-15 00:00:00 downloaded\n", "ds: 2024-11-16 00:00:00 downloaded\n", "ds: 2024-11-17 00:00:00 downloaded\n", "ds: 2024-11-18 00:00:00 downloaded\n", "ds: 2024-11-19 00:00:00 downloaded\n", "ds: 2024-11-20 00:00:00 downloaded\n", "keep: 4897, stop: 176050\n", "df.shape: (556218, 71)\n", "distinct adid: 231193\n", "error opt: 376\n", "type   pay7        cost  count     roi\n", " 保留前 953462  8434843.18   4848 0.11304\n", " 关停前 408976 12171647.62 175623 0.03360\n", "未满观测  16478   211699.36   3584 0.07784\n", " 保留后 875370 11188732.46   3219 0.07824\n", " 关停后 230004  3438907.56  19279 0.06688\n", "早于开始 211462  2126385.33   7549 0.09945\n"]}], "source": ["from datetime import datetime, timedelta\n", "from collections import Counter\n", "import numpy as np\n", "import pandas as pd\n", "from pprint import pprint\n", "import pymysql\n", "\n", "def evaluation_in_mysql(df, mysql_start_ds, mysql_end_ds):\n", "    observe_day=4\n", "\n", "    # 获取 MYSQL 数据\n", "    config = {  \"host\": \"**************\", \"port\": 6011, \"user\": \"root\", \"password\": \"Ao1JvHBIiiwkSYq5\", \"database\": \"ltv_prediction_fish_3d\", \"charset\": 'utf8mb4'}\n", "    conn = pymysql.connect(**config)\n", "    cursor = conn.cursor()\n", "    sql = f\"select ad_id, ds, action_taken FROM ltv_prediction_fish_3d.ad_intervention_history where ds between '{mysql_start_ds}' and '{mysql_end_ds}' and model_name = 'ad_intervention_cbc_v20250507' \"\n", "    cursor.execute(sql)\n", "    ad_ds_list = [(item[0], item[1], item[2]) for item in cursor.fetchall()]\n", "    ad_stop = {int(ad): ds.strftime('%Y-%m-%d') for ad, ds, action in ad_ds_list if action == 'STOP'}\n", "    ad_keep = {int(ad): ds.strftime('%Y-%m-%d') for ad, ds, action in ad_ds_list if action == 'KEEP'}\n", "    print(f'keep: {len(ad_keep)}, stop: {len(ad_stop)}')\n", "\n", "    # 统计结果  \n", "    name_mapping = {\n", "        ('before','keep'): '保留前',\n", "        ('before', 'stop'): '关停前',\n", "        ('before', 'other'): '未满观测',\n", "        ('after', 'keep'): '保留后',\n", "        ('after', 'stop'): '关停后',\n", "        ('after', 'other'): '早于开始',\n", "    }\n", "    result = {}\n", "    for tt in ['before', 'after']:\n", "        for opt_type in ['keep', 'stop', 'other', 'error']:\n", "            result[(tt, opt_type)] = {\"pay7\": 0, 'cost': 0, \"count\": set()}\n", "            \n", "    print(f\"df.shape: {df.shape}\")\n", "    print(f\"distinct adid: {len(set(df['ad_id'].tolist()))}\")\n", "    error_opt = set()\n", "    keep_cnt = Counter()\n", "    stop_cnt = Counter()\n", "    error_keep = set()\n", "    error_stop = set()\n", "    \n", "    # 遍历，\n", "    for index, row in df.iterrows():\n", "        # if index > 2500:\n", "            # break\n", "        if row['ad_id'] in ad_keep:\n", "            opt = 'keep'\n", "            tt = 'before' if row['dt'].strftime('%Y-%m-%d') <= ad_keep[row['ad_id']] else 'after'\n", "            keep_at = (pd.to_datetime(ad_keep[row['ad_id']]) - row['dt']).days + row['delay']\n", "            if keep_at != 3:\n", "                error_keep.add(row['ad_id'])\n", "            # print(tt, opt)\n", "            # print(f\"pay: {row['pay7']}, cost: {row['cost']}\")\n", "            # print(f\"delay: {row['delay']}, dt: {row['dt'].strftime('%Y-%m-%d')}\")\n", "            # print(f\"keep at: {ad_keep[row['ad_id']]}\")\n", "        elif row['ad_id'] in ad_stop:\n", "            opt = 'stop'\n", "            tt = 'before' if row['dt'].strftime('%Y-%m-%d') <= ad_stop[row['ad_id']] else 'after'\n", "            stop_at = (pd.to_datetime(ad_stop[row['ad_id']]) - row['dt']).days + row['delay']\n", "            # print(tt, opt)\n", "            # print(f\"pay: {row['pay7']}, cost: {row['cost']}\")\n", "            # print(f\"delay: {row['delay']}, dt: {row['dt'].strftime('%Y-%m-%d')}\")\n", "            # print(f\"stop at: {ad_stop[row['ad_id']]}\")\n", "            if stop_at != 3:\n", "                error_stop.add(row['ad_id'])\n", "        else:\n", "            opt = 'other'\n", "            tt = 'before' if row['delay'] <= 3 else 'after'\n", "        \n", "        # if row['ad_id'] in ad_stop:\n", "        #     if row['dt'].strftime('%Y-%m-%d') < ad_stop[row['ad_id']] and row['delay'] >= 4:\n", "        #         error_opt.add(row['ad_id'])\n", "        #     stop_cnt[(pd.to_datetime(ad_stop[row['ad_id']]) - row['dt']).days + row['delay']] += 1\n", "        # if row['ad_id'] in ad_keep:\n", "        #     if row['dt'].strftime('%Y-%m-%d') < ad_keep[row['ad_id']] and row['delay'] >= 4:\n", "        #         error_opt.add(row['ad_id'])\n", "        #     keep_cnt[(pd.to_datetime(ad_keep[row['ad_id']]) - row['dt']).days + row['delay']] += 1\n", "\n", "    raw_data = {}\n", "    for index, row in df.iterrows():\n", "        if row['dt'] > datetime.now() - <PERSON><PERSON><PERSON>(days=7):\n", "            continue\n", "        if row['ad_id'] in ad_keep and row['ad_id'] not in error_keep:\n", "            opt = 'keep'\n", "            tt = 'before' if row['dt'].strftime('%Y-%m-%d') <= ad_keep[row['ad_id']] else 'after'\n", "            if tt == 'after' and (row['dt'] - pd.to_datetime(ad_keep[row['ad_id']])).days > window_size:\n", "                continue\n", "            # print(tt, opt)\n", "            # print(f\"pay: {row['pay7']}, cost: {row['cost']}\")\n", "            # print(f\"delay: {row['delay']}, dt: {row['dt'].strftime('%Y-%m-%d')}\")\n", "            # print(f\"keep at: {ad_keep[row['ad_id']]}\")\n", "        elif row['ad_id'] in ad_stop and row['ad_id'] not in error_stop:\n", "            opt = 'stop'\n", "            tt = 'before' if row['dt'].strftime('%Y-%m-%d') <= ad_stop[row['ad_id']] else 'after'\n", "            if tt == 'after' and (row['dt'] - pd.to_datetime(ad_stop[row['ad_id']])).days > window_size:\n", "                continue\n", "            # stop_at = (pd.to_datetime(ad_stop[row['ad_id']]) - row['dt']).days + row['delay']\n", "            # print(tt, opt)\n", "            # print(f\"pay: {row['pay7']}, cost: {row['cost']}\")\n", "            # print(f\"delay: {row['delay']}, dt: {row['dt'].strftime('%Y-%m-%d')}\")\n", "            # print(f\"stop at: {ad_stop[row['ad_id']]}\")\n", "            # if stop_at != 3:\n", "                # error_stop.add(row['ad_id'])\n", "        elif row['ad_id'] not in ad_stop and row['ad_id'] not in ad_keep:\n", "            if (datetime.strptime(mysql_start_ds, '%Y-%m-%d') - timedelta(days=3)).strftime('%Y-%m-%d') <= row['create_ds'] <= mysql_end_ds:\n", "                if row['delay'] < 3:\n", "                    opt = 'other'\n", "                    tt = 'before'\n", "                else:\n", "                    opt = 'error'\n", "                    tt = 'before'\n", "            elif row['create_ds'] < (datetime.strptime(mysql_start_ds, '%Y-%m-%d') - timedelta(days=3)).strftime('%Y-%m-%d'):\n", "                opt = 'other'\n", "                tt = 'after'\n", "            else:\n", "                continue\n", "                \n", "        else:\n", "            continue\n", "\n", "        result[(tt, opt)]['pay7'] += row['pay7']\n", "        result[(tt, opt)]['cost'] += row['cost']\n", "        result[(tt, opt)]['count'].add(row['ad_id'])     \n", "        if row['ad_id'] not in raw_data:\n", "            raw_data[row['ad_id']] = [0, 0, [], Counter()] # pay, cost, create_dt, opt tt\n", "        name = str(name_mapping.get((tt, opt)))\n", "        if name.startswith('关停'):\n", "            if name.endswith('后'):\n", "                raw_data[row['ad_id']][0] += row['pay7']\n", "                raw_data[row['ad_id']][1] += row['cost']\n", "            raw_data[row['ad_id']][2].append((row['dt'].strftime('%Y-%m-%d'), row['pay7'], row['cost']))\n", "            raw_data[row['ad_id']][3][name_mapping.get((tt, opt))] += 1\n", "\n", "    s_raw_data = sorted(raw_data.items(), key=lambda x: x[1][0], reverse=True)\n", "    # pprint(s_raw_data[:100])\n", "        \n", "        \n", "        \n", "    print(f'error opt: {len(error_keep) + len(error_stop)}')\n", "    # print(stop_cnt)\n", "    # print(keep_cnt)\n", "    all_result = []\n", "\n", "    \n", "    total_pay7, total_cost = 0, 0\n", "    for tt, opt in result:\n", "        # print('=' * 30)\n", "        # print(tt, opt)\n", "        # print(f\"pay7: {result[(tt, opt)]['pay7']}\")\n", "        # print(f\"cost: {result[(tt, opt)]['cost']:.2f}\")\n", "        # print(f\"count: {len(result[(tt, opt)]['count'])}\")\n", "        # print(f\"roi: {result[(tt, opt)]['pay7'] / (result[(tt, opt)]['cost'] + 1e-8):.5f}\")\n", "        if (tt, opt) in name_mapping:\n", "            name = name_mapping[(tt, opt)]\n", "            all_result.append({\n", "                \"type\": name,\n", "                'pay7': f\"{result[(tt, opt)]['pay7']}\",\n", "                'cost': f\"{result[(tt, opt)]['cost']:.2f}\",\n", "                'count': len(result[(tt, opt)]['count']),\n", "                'roi': f\"{result[(tt, opt)]['pay7'] / (result[(tt, opt)]['cost'] + 1e-8):.5f}\",\n", "            })\n", "    result_df = pd.DataFrame(all_result)\n", "    print(result_df.to_string(line_width=500, index=False))\n", "\n", "\n", "# 获取 TD 数据\n", "window_size = 30\n", "mysql_start_ds, mysql_end_ds = '2024-10-01', '2025-04-01'\n", "td_start_ds = (datetime.strptime(mysql_start_ds, '%Y-%m-%d') - timedelta(days=10)).strftime('%Y-%m-%d')\n", "td_end_ds = (datetime.strptime(mysql_end_ds, '%Y-%m-%d') + timedelta(days=window_size)).strftime('%Y-%m-%d')\n", "# td_start_ds = '2025-01-01'\n", "# td_end_ds = '2025-05-07'\n", "__fetched = fetch_all_data(start_ds=td_start_ds, end_ds=td_end_ds)\n", "\n", "df = pd.concat([__fetched[ds] for ds in __fetched])\n", "df['dt'] = pd.to_datetime(df['date_start'])\n", "df['create_ds'] = df.groupby('ad_id')['date_start'].transform('min')\n", "df['create_dt'] = pd.to_datetime(df['create_ds'])\n", "df['delay'] = (df['dt'] - df['create_dt']).dt.days\n", "\n", "evaluation_in_mysql(df, mysql_start_ds=mysql_start_ds, mysql_end_ds=mysql_end_ds)\n", "\n", "# print(df.head(10).to_string())\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "811cfacc-b438-4baf-8e9b-46c7de89a1a5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}