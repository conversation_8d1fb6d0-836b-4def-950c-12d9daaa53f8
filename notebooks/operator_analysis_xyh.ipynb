{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pickle\n", "from typing import Dict\n", "\n", "def load_operation_logs():\n", "    \"\"\" 20250225 ~ 20250430 \"\"\"\n", "    file_path = '/wkspace/qiushi/ad-intervention/data/operation_logs_20250519.csv'\n", "    operation_df = pd.read_csv(file_path)\n", "    return operation_df\n", "\n", "\n", "def load_td_data(filepath, start_ds=None, end_ds=None):\n", "    with open(filepath, 'rb') as f:\n", "        new_fetched_data = pickle.load(f)\n", "    if start_ds is None or end_ds is None:\n", "        return new_fetched_data\n", "    return {ds: new_fetched_data[ds] for ds in pd.date_range(start=start_ds, end=end_ds)}\n", "\n", "\n", "def process_td_df(td_df: pd.DataFrame, min_ds: str, min_opt_days: Dict[str, str]):\n", "    td_df['dt'] = pd.to_datetime(td_df['date_start'])\n", "    td_df['create_ds'] = td_df.groupby('ad_id')['date_start'].transform('min')\n", "    td_df['create_dt'] = pd.to_datetime(td_df['create_ds'])\n", "    td_df['delay'] = (td_df['dt'] - td_df['create_dt']).dt.days\n", "    td_df['max_delay'] = td_df.groupby('ad_id')['delay'].transform('max')\n", "    # print('checkpoint', str(datetime.now()))\n", "    # valid_ad = td_df[(td_df['create_dt'] >= pd.to_datetime(feature_start_ds)) & (td_df['create_dt'] <= pd.to_datetime(feature_end_ds))]\n", "    # print(f\"valid_ad.shape: {valid_ad.shape}\")\n", "    # valid_ad_dead = valid_ad[valid_ad['max_delay'] < observe_day]\n", "    # valid_ad_survive_observe = valid_ad[(valid_ad['max_delay'] >= observe_day) & (valid_ad['delay'] < observe_day)]\n", "    # valid_ad_survive_unknown = valid_ad[(valid_ad['max_delay'] >= observe_day) & (valid_ad['delay'] >= observe_day)]\n", "    # print(f\"valid_ad_dead.shape: {valid_ad_dead.shape}\")\n", "    # print(f\"valid_ad_survive_observe.shape: {valid_ad_survive_observe.shape}\")\n", "    # print(f\"valid_ad_survive_unknown.shape: {valid_ad_survive_unknown.shape}\")\n", "\n", "operation_df = load_operation_logs()\n", "# td_data = load_td_data(f'../data/td_data_2025-01-01_2025-05-06.pkl')\n", "# process_td_df(pd.concat(td_data.values()))\n", "print(operation_df)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}