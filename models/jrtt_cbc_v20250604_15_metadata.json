{"feature_names": ["day0.cost", "day0.show", "day0.click", "day0.cost_by_thousand_show", "day0.new_user", "day0.new_paid_user", "day0.new_user_cost", "day0.new_paid_user_cost", "day0.cvr", "day0.ctr", "day0.convert", "day0.active_cost", "day0.convert_cost", "day0.active", "day0.pay1", "day0.pay2", "day0.pay3", "day0.pay4", "day0.pay5", "day0.pay6", "day0.pay7", "day0.ltv1", "day0.ltv2", "day0.ltv3", "day0.ltv4", "day0.ltv5", "day0.ltv6", "day0.ltv7", "day0.roi1", "day0.roi2", "day0.roi3", "day0.roi4", "day0.roi5", "day0.roi6", "day0.roi7", "day0.stay_num2", "day0.stay_num3", "day0.stay_num4", "day0.stay_num5", "day0.stay_num6", "day0.stay_num7", "day0.new_user_ad_trace", "day0.pay1_ad_trace", "day1.cost", "day1.show", "day1.click", "day1.cost_by_thousand_show", "day1.new_user", "day1.new_paid_user", "day1.new_user_cost", "day1.new_paid_user_cost", "day1.cvr", "day1.ctr", "day1.convert", "day1.active_cost", "day1.convert_cost", "day1.active", "day1.pay1", "day1.pay2", "day1.pay3", "day1.pay4", "day1.pay5", "day1.pay6", "day1.pay7", "day1.ltv1", "day1.ltv2", "day1.ltv3", "day1.ltv4", "day1.ltv5", "day1.ltv6", "day1.ltv7", "day1.roi1", "day1.roi2", "day1.roi3", "day1.roi4", "day1.roi5", "day1.roi6", "day1.roi7", "day1.stay_num2", "day1.stay_num3", "day1.stay_num4", "day1.stay_num5", "day1.stay_num6", "day1.stay_num7", "day1.new_user_ad_trace", "day1.pay1_ad_trace", "day2.cost", "day2.show", "day2.click", "day2.cost_by_thousand_show", "day2.new_user", "day2.new_paid_user", "day2.new_user_cost", "day2.new_paid_user_cost", "day2.cvr", "day2.ctr", "day2.convert", "day2.active_cost", "day2.convert_cost", "day2.active", "day2.pay1", "day2.pay2", "day2.pay3", "day2.pay4", "day2.pay5", "day2.pay6", "day2.pay7", "day2.ltv1", "day2.ltv2", "day2.ltv3", "day2.ltv4", "day2.ltv5", "day2.ltv6", "day2.ltv7", "day2.roi1", "day2.roi2", "day2.roi3", "day2.roi4", "day2.roi5", "day2.roi6", "day2.roi7", "day2.stay_num2", "day2.stay_num3", "day2.stay_num4", "day2.stay_num5", "day2.stay_num6", "day2.stay_num7", "day2.new_user_ad_trace", "day2.pay1_ad_trace", "day3.cost", "day3.show", "day3.click", "day3.cost_by_thousand_show", "day3.new_user", "day3.new_paid_user", "day3.new_user_cost", "day3.new_paid_user_cost", "day3.cvr", "day3.ctr", "day3.convert", "day3.active_cost", "day3.convert_cost", "day3.active", "day3.pay1", "day3.pay2", "day3.pay3", "day3.pay4", "day3.pay5", "day3.pay6", "day3.pay7", "day3.ltv1", "day3.ltv2", "day3.ltv3", "day3.ltv4", "day3.ltv5", "day3.ltv6", "day3.ltv7", "day3.roi1", "day3.roi2", "day3.roi3", "day3.roi4", "day3.roi5", "day3.roi6", "day3.roi7", "day3.stay_num2", "day3.stay_num3", "day3.stay_num4", "day3.stay_num5", "day3.stay_num6", "day3.stay_num7", "day3.new_user_ad_trace", "day3.pay1_ad_trace", "day4.cost", "day4.show", "day4.click", "day4.cost_by_thousand_show", "day4.new_user", "day4.new_paid_user", "day4.new_user_cost", "day4.new_paid_user_cost", "day4.cvr", "day4.ctr", "day4.convert", "day4.active_cost", "day4.convert_cost", "day4.active", "day4.pay1", "day4.pay2", "day4.pay3", "day4.pay4", "day4.pay5", "day4.pay6", "day4.pay7", "day4.ltv1", "day4.ltv2", "day4.ltv3", "day4.ltv4", "day4.ltv5", "day4.ltv6", "day4.ltv7", "day4.roi1", "day4.roi2", "day4.roi3", "day4.roi4", "day4.roi5", "day4.roi6", "day4.roi7", "day4.stay_num2", "day4.stay_num3", "day4.stay_num4", "day4.stay_num5", "day4.stay_num6", "day4.stay_num7", "day4.new_user_ad_trace", "day4.pay1_ad_trace", "day5.cost", "day5.show", "day5.click", "day5.cost_by_thousand_show", "day5.new_user", "day5.new_paid_user", "day5.new_user_cost", "day5.new_paid_user_cost", "day5.cvr", "day5.ctr", "day5.convert", "day5.active_cost", "day5.convert_cost", "day5.active", "day5.pay1", "day5.pay2", "day5.pay3", "day5.pay4", "day5.pay5", "day5.pay6", "day5.pay7", "day5.ltv1", "day5.ltv2", "day5.ltv3", "day5.ltv4", "day5.ltv5", "day5.ltv6", "day5.ltv7", "day5.roi1", "day5.roi2", "day5.roi3", "day5.roi4", "day5.roi5", "day5.roi6", "day5.roi7", "day5.stay_num2", "day5.stay_num3", "day5.stay_num4", "day5.stay_num5", "day5.stay_num6", "day5.stay_num7", "day5.new_user_ad_trace", "day5.pay1_ad_trace", "day6.cost", "day6.show", "day6.click", "day6.cost_by_thousand_show", "day6.new_user", "day6.new_paid_user", "day6.new_user_cost", "day6.new_paid_user_cost", "day6.cvr", "day6.ctr", "day6.convert", "day6.active_cost", "day6.convert_cost", "day6.active", "day6.pay1", "day6.pay2", "day6.pay3", "day6.pay4", "day6.pay5", "day6.pay6", "day6.pay7", "day6.ltv1", "day6.ltv2", "day6.ltv3", "day6.ltv4", "day6.ltv5", "day6.ltv6", "day6.ltv7", "day6.roi1", "day6.roi2", "day6.roi3", "day6.roi4", "day6.roi5", "day6.roi6", "day6.roi7", "day6.stay_num2", "day6.stay_num3", "day6.stay_num4", "day6.stay_num5", "day6.stay_num6", "day6.stay_num7", "day6.new_user_ad_trace", "day6.pay1_ad_trace", "day7.cost", "day7.show", "day7.click", "day7.cost_by_thousand_show", "day7.new_user", "day7.new_paid_user", "day7.new_user_cost", "day7.new_paid_user_cost", "day7.cvr", "day7.ctr", "day7.convert", "day7.active_cost", "day7.convert_cost", "day7.active", "day7.pay1", "day7.pay2", "day7.pay3", "day7.pay4", "day7.pay5", "day7.pay6", "day7.pay7", "day7.ltv1", "day7.ltv2", "day7.ltv3", "day7.ltv4", "day7.ltv5", "day7.ltv6", "day7.ltv7", "day7.roi1", "day7.roi2", "day7.roi3", "day7.roi4", "day7.roi5", "day7.roi6", "day7.roi7", "day7.stay_num2", "day7.stay_num3", "day7.stay_num4", "day7.stay_num5", "day7.stay_num6", "day7.stay_num7", "day7.new_user_ad_trace", "day7.pay1_ad_trace", "day8.cost", "day8.show", "day8.click", "day8.cost_by_thousand_show", "day8.new_user", "day8.new_paid_user", "day8.new_user_cost", "day8.new_paid_user_cost", "day8.cvr", "day8.ctr", "day8.convert", "day8.active_cost", "day8.convert_cost", "day8.active", "day8.pay1", "day8.pay2", "day8.pay3", "day8.pay4", "day8.pay5", "day8.pay6", "day8.pay7", "day8.ltv1", "day8.ltv2", "day8.ltv3", "day8.ltv4", "day8.ltv5", "day8.ltv6", "day8.ltv7", "day8.roi1", "day8.roi2", "day8.roi3", "day8.roi4", "day8.roi5", "day8.roi6", "day8.roi7", "day8.stay_num2", "day8.stay_num3", "day8.stay_num4", "day8.stay_num5", "day8.stay_num6", "day8.stay_num7", "day8.new_user_ad_trace", "day8.pay1_ad_trace", "day8.pay7_ad_trace", "day9.cost", "day9.show", "day9.click", "day9.cost_by_thousand_show", "day9.new_user", "day9.new_paid_user", "day9.new_user_cost", "day9.new_paid_user_cost", "day9.cvr", "day9.ctr", "day9.convert", "day9.active_cost", "day9.convert_cost", "day9.active", "day9.pay1", "day9.pay2", "day9.pay3", "day9.pay4", "day9.pay5", "day9.pay6", "day9.ltv1", "day9.ltv2", "day9.ltv3", "day9.ltv4", "day9.ltv5", "day9.ltv6", "day9.roi1", "day9.roi2", "day9.roi3", "day9.roi4", "day9.roi5", "day9.roi6", "day9.stay_num2", "day9.stay_num3", "day9.stay_num4", "day9.stay_num5", "day9.stay_num6", "day9.new_user_ad_trace", "day9.pay1_ad_trace", "day10.cost", "day10.show", "day10.click", "day10.cost_by_thousand_show", "day10.new_user", "day10.new_paid_user", "day10.new_user_cost", "day10.new_paid_user_cost", "day10.cvr", "day10.ctr", "day10.convert", "day10.active_cost", "day10.convert_cost", "day10.active", "day10.pay1", "day10.pay2", "day10.pay3", "day10.pay4", "day10.pay5", "day10.ltv1", "day10.ltv2", "day10.ltv3", "day10.ltv4", "day10.ltv5", "day10.roi1", "day10.roi2", "day10.roi3", "day10.roi4", "day10.roi5", "day10.stay_num2", "day10.stay_num3", "day10.stay_num4", "day10.stay_num5", "day10.new_user_ad_trace", "day10.pay1_ad_trace", "day11.cost", "day11.show", "day11.click", "day11.cost_by_thousand_show", "day11.new_user", "day11.new_paid_user", "day11.new_user_cost", "day11.new_paid_user_cost", "day11.cvr", "day11.ctr", "day11.convert", "day11.active_cost", "day11.convert_cost", "day11.active", "day11.pay1", "day11.pay2", "day11.pay3", "day11.pay4", "day11.ltv1", "day11.ltv2", "day11.ltv3", "day11.ltv4", "day11.roi1", "day11.roi2", "day11.roi3", "day11.roi4", "day11.stay_num2", "day11.stay_num3", "day11.stay_num4", "day11.new_user_ad_trace", "day11.pay1_ad_trace", "day12.cost", "day12.show", "day12.click", "day12.cost_by_thousand_show", "day12.new_user", "day12.new_paid_user", "day12.new_user_cost", "day12.new_paid_user_cost", "day12.cvr", "day12.ctr", "day12.convert", "day12.active_cost", "day12.convert_cost", "day12.active", "day12.pay1", "day12.pay2", "day12.pay3", "day12.ltv1", "day12.ltv2", "day12.ltv3", "day12.roi1", "day12.roi2", "day12.roi3", "day12.stay_num2", "day12.stay_num3", "day12.new_user_ad_trace", "day12.pay1_ad_trace", "day13.cost", "day13.show", "day13.click", "day13.cost_by_thousand_show", "day13.new_user", "day13.new_paid_user", "day13.new_user_cost", "day13.new_paid_user_cost", "day13.cvr", "day13.ctr", "day13.convert", "day13.active_cost", "day13.convert_cost", "day13.active", "day13.pay1", "day13.pay2", "day13.ltv1", "day13.ltv2", "day13.roi1", "day13.roi2", "day13.stay_num2", "day13.new_user_ad_trace", "day13.pay1_ad_trace", "day14.cost", "day14.show", "day14.click", "day14.cost_by_thousand_show", "day14.new_user", "day14.new_paid_user", "day14.new_user_cost", "day14.new_paid_user_cost", "day14.cvr", "day14.ctr", "day14.convert", "day14.active_cost", "day14.convert_cost", "day14.active", "day14.pay1", "day14.ltv1", "day14.roi1", "day14.new_user_ad_trace", "day14.pay1_ad_trace"], "feature_types": ["float64", "int64", "int64", "float64", "int64", "int64", "float64", "float64", "float64", "float64", "int64", "float64", "float64", "int64", "int64", "int64", "int64", "int64", "int64", "int64", "int64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "int64", "int64", "int64", "int64", "int64", "int64", "int64", "int64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64"], "params": {"observe_day": 15, "label_range": 30, "label_day": 7, "roi_threshold": 0.04, "cost_threshold": 200, "predict_threshold": 0.4}}