{"feature_names": ["day0.cost", "day0.show", "day0.click", "day0.cost_by_thousand_show", "day0.new_user", "day0.new_paid_user", "day0.new_user_cost", "day0.new_paid_user_cost", "day0.cvr", "day0.ctr", "day0.convert", "day0.active_cost", "day0.convert_cost", "day0.active", "day0.pay1", "day0.pay2", "day0.pay3", "day0.pay4", "day0.ltv1", "day0.ltv2", "day0.ltv3", "day0.ltv4", "day0.roi1", "day0.roi2", "day0.roi3", "day0.roi4", "day0.stay_num2", "day0.stay_num3", "day0.stay_num4", "day0.new_user_ad_trace", "day0.pay1_ad_trace", "day1.cost", "day1.show", "day1.click", "day1.cost_by_thousand_show", "day1.new_user", "day1.new_paid_user", "day1.new_user_cost", "day1.new_paid_user_cost", "day1.cvr", "day1.ctr", "day1.convert", "day1.active_cost", "day1.convert_cost", "day1.active", "day1.pay1", "day1.pay2", "day1.pay3", "day1.ltv1", "day1.ltv2", "day1.ltv3", "day1.roi1", "day1.roi2", "day1.roi3", "day1.stay_num2", "day1.stay_num3", "day1.new_user_ad_trace", "day1.pay1_ad_trace", "day2.cost", "day2.show", "day2.click", "day2.cost_by_thousand_show", "day2.new_user", "day2.new_paid_user", "day2.new_user_cost", "day2.new_paid_user_cost", "day2.cvr", "day2.ctr", "day2.convert", "day2.active_cost", "day2.convert_cost", "day2.active", "day2.pay1", "day2.pay2", "day2.ltv1", "day2.ltv2", "day2.roi1", "day2.roi2", "day2.stay_num2", "day2.new_user_ad_trace", "day2.pay1_ad_trace", "day3.cost", "day3.show", "day3.click", "day3.cost_by_thousand_show", "day3.new_user", "day3.new_paid_user", "day3.new_user_cost", "day3.new_paid_user_cost", "day3.cvr", "day3.ctr", "day3.convert", "day3.active_cost", "day3.convert_cost", "day3.active", "day3.pay1", "day3.ltv1", "day3.roi1", "day3.new_user_ad_trace", "day3.pay1_ad_trace"], "feature_types": ["float64", "int64", "int64", "float64", "int64", "int64", "float64", "float64", "float64", "float64", "int64", "float64", "float64", "int64", "int64", "int64", "int64", "int64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "int64", "int64", "int64", "int64", "int64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64", "float64"], "params": {"observe_day": 4, "label_range": 30, "label_day": 7, "roi_threshold": 0.04, "cost_threshold": 200}}