import os
os.environ['PYTHONIOENCODING'] = 'utf-8'

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import argparse
from datetime import datetime, timedelta, date
import time
import schedule
import pytz
import importlib
from pprint import pprint
import traceback
import pandas as pd

from src.utils.logger import setup_logger
from src.config import AdConfig


def run(cls, job_config, global_config):
    logger = setup_logger("ad-intervention-logger", global_config["path"]["log_path"])
    logger.info("start ad-intervention")
    result = cls(**job_config, _config=global_config).run()
    logger.info(result)


def main(run_forever=False, ds=None, start_ds=None, end_ds=None, config_file=None, env_file=""):   
    assert run_forever or ds is not None or (start_ds is not None and end_ds is not None)
    _config = AdConfig(config_path=config_file, env_path=env_file)
    
    if run_forever:
        for job_config in _config['jobs']:
            class_path = job_config['class_path']
            module_path, class_name = class_path.rsplit(".", 1)
            module = importlib.import_module(module_path)
            cls = getattr(module, class_name)
            if 'at' in job_config:
                schedule.every().day.at(job_config['at'], "Asia/Shanghai").do(run, cls=cls, job_config=job_config, global_config=_config)
            else:
                schedule.every(job_config['interval']).seconds.do(run, cls=cls, job_config=job_config, global_config=_config)
        while True:
            schedule.run_pending()
            time.sleep(1)
            
    else:
        if ds is not None:
            start_ds = ds
            end_ds = ds
    
        for ds in pd.date_range(start_ds, end_ds):
            for job_config in _config['jobs']:
                class_path = job_config['class_path']
                module_path, class_name = class_path.rsplit(".", 1)
                module = importlib.import_module(module_path)
                cls = getattr(module, class_name)
                job_config['ds'] = ds.strftime('%Y-%m-%d')
                print(job_config)
                run(cls=cls, job_config=job_config, global_config=_config)
            

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--forever', action='store_true', help='是否一直运行')
    parser.add_argument('--ds', type=str, default=None, help='运行日期T-1（会覆盖start_ds和end_ds）')
    parser.add_argument('--start_ds', type=str, default=None, help='开始日期')
    parser.add_argument('--end_ds', type=str, default=None, help='结束日期')
    parser.add_argument('--config', type=str, required=True, help='配置文件')
    parser.add_argument('--env', type=str, default="", help='环境配置')
    args = parser.parse_args()
    
    main(run_forever=args.forever, ds=args.ds, start_ds=args.start_ds, end_ds=args.end_ds, 
         config_file=args.config, env_file=args.env)
    
    # 3D捕鱼，生产环境，常驻运行，指定环境文件
    # python -u main.py --forever --config configs/config-prod-3d.yaml --env .env.prod
    # 3D捕鱼，开发环境，指定日期运行
    # python -u main.py --ds 2025-04-16 --config configs/config-dev-3d.yaml --env .env.dev
    # UE5捕鱼，生产环境，指定日期范围
    # python -u src/main.py --start_ds 2025-06-18 --end_ds 2025-06-24 --config config-prod-ue5fishing.yaml --env .env.prod
    