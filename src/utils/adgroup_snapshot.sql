select 
    from_unixtime(cast(event_time/1000 as bigint), 'yyyy-MM-dd') as snap_day,
    from_unixtime(cast(event_time/1000 as bigint), 'HH:mm:ss') as snap_time,
    platform_id,
    channel_id,
    adgroup_id,
    adgroup_activation,
    adgroup_bid,
    adgroup_c,
    adgroup_i,
    adgroup_pay1,
    adgroup_pay_user,
    adgroup_roi1,
    adgroup_roi_bid,
    adgroup_s,
    adgroup_status,
    media_source
from 
    table.event_20567 
where 
    event_id = 'adgroup_realtime_attribute_snapshot' 
    and td_project_name = '3D捕鱼'
    and media_source = '1'
    and day between '{start_day}' and '{end_day}'
order by 
    operation_time