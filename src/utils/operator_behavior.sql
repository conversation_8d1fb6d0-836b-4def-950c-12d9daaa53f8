select 
    substring(operation_time,1,10) as operation_day,
    substring(operation_time,11,19) as operation_time, 
    advertiser_studio_id,
    advertiser_id, 
    optimizer_name,
    channel_operator, 
    operation_object_id, 
    operation_object_name, 
    operation_target, 
    operation_type,
    operation_log
from 
    table.event_20567
where 
    event_id = 'channel_operation_logs' 
    and td_project_name = '3D捕鱼'
    and day between '{start_day}' and '{end_day}'
    and media_source = '1'
    and substring(operation_time,1,10) between '{start_day}' and '{end_day}'
order by 
    event_time 