select 
    from_unixtime(cast(event_time/1000 as bigint), 'yyyy-MM-dd') as snap_day,
    from_unixtime(cast(event_time/1000 as bigint), 'HH:mm:ss') as snap_time,
    platform_id,
    channel_id,
    ad_id,
    ad_activation,
    ad_bid,
    ad_budget,
    ad_c,
    ad_i,
    ad_pay1,
    ad_pay_user,
    ad_roi1,
    ad_roi_bid,
    ad_s,
    ad_status,
    media_source
from 
    table.event_20567 
where 
    event_id = 'ad_realtime_attribute_snapshot' 
    and td_project_name = '3D捕鱼'
    and media_source = '1'
    and day between '{start_day}' and '{end_day}'
order by 
    operation_time