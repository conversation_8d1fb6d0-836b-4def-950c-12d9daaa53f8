select 
    from_unixtime(cast(event_time/1000 as bigint), 'yyyy-MM-dd') as snap_day,
    from_unixtime(cast(event_time/1000 as bigint), 'HH:mm:ss') as snap_time,
    platform_id,
    channel_id,
    advertiser_budget,
    advertiser_id,
    advertiser_activation,
    advertiser_c,
    advertiser_i,
    advertiser_pay1,
    advertiser_pay_user,
    advertiser_roi1,
    advertiser_s,
    media_source
from 
    table.event_20567 
where 
    event_id = 'advertiser_realtime_attribute_snapshot' 
    and td_project_name = '3D捕鱼'
    and media_source = '1'
    and day between '{start_day}' and '{end_day}'
order by 
    operation_time