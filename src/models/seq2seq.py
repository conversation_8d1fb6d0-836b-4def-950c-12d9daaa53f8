
import torch
import torch.nn as nn
import pytorch_lightning as pl
from torch.nn import functional as F
from dataset.seq2seq_loader import AdsDataModule
from torchmetrics import Metric
import holidays
from datetime import datetime, timedelta
from typing import List
import argparse
import yaml


class SumMetric(Metric):
    def __init__(self):
        super().__init__()
        self.add_state("sum_value", default=torch.tensor(0.0), dist_reduce_fx="sum")

    def update(self, value):
        self.sum_value += value.sum()

    def compute(self):
        return self.sum_value


class AdsTransformer(pl.LightningModule):
    def __init__(self, input_dim, model_dim, num_heads, num_layers, output_dim, 
                 max_seq_len=100, lr=1e-3, observe_day=3, class_weight=[0.05, 0.95], sample_weight=None,
                 norm_type='batch', time_embedding=False, adtrace_embedding=False, device_embedding=False, 
                 time_dim=16, add_cls=False, **kwargs):
        super().__init__()
        self.save_hyperparameters()
        
        self.time_embedding = time_embedding
        self.adtrace_embedding = adtrace_embedding
        self.device_embedding = device_embedding
        self.norm_type = norm_type
        self.pos_curve = kwargs.get('pos_curve', 0.3)
        self.weight_decay = kwargs.get('weight_decay', 0.0)
        self.mask_observe = kwargs.get('mask_observe', True)
        self.add_cls = add_cls
        
        if norm_type == 'batch':
            self.input_norm = nn.BatchNorm1d(input_dim)
        elif norm_type == 'layer':
            self.input_norm = nn.LayerNorm(input_dim)
        else:
            self.input_norm = nn.Identity()
            
        if time_embedding:
            self.input_proj = nn.Linear(input_dim, model_dim - time_dim)
            self.positional_encoding = PositionalEncoding(model_dim - time_dim, max_len=max_seq_len)
            self.time_layer = TimeEmbedding(time_dim)
        else:
            self.input_proj = nn.Linear(input_dim, model_dim)
            self.positional_encoding = PositionalEncoding(model_dim, max_len=max_seq_len)
            self.time_layer = nn.Identity()
        # self.adtrace_proj = AdtraceEmbedding(model_dim)
        # self.device_proj = DeviceEmbedding(model_dim)
        decoder_layer = nn.TransformerDecoderLayer(d_model=model_dim, nhead=num_heads, batch_first=True)
        self.transformer_decoder = nn.TransformerDecoder(decoder_layer, num_layers=num_layers)
        self.output_proj = nn.Linear(model_dim, output_dim)
        
        self.lr = lr
        self.observe_day = observe_day
        self.class_weight = torch.tensor(class_weight)
        self.sample_weight = sample_weight
        assert self.sample_weight in (None, 'log1p')
        
        # Metrics
        self.sum_cost_get = SumMetric()
        self.sum_revenue_get = SumMetric()
        self.sum_cost_discard = SumMetric()
        self.sum_revenue_discard = SumMetric()

        
    def make_tgt_mask(self, sz):
        """Generate a triangular mask"""
        return torch.triu(torch.ones(sz, sz) * float('-inf'), diagonal=1)


    def forward(self, x, src_key_padding_mask=None, 
                ds_list: List[str] = None, 
                # sdk_packages: List[str] = None, aids: List[str] = None, acts: List[str] = None, proj_main_channel_ids: List[str] = None
                ):
        """
        x: (batch_size, seq_len, input_dim)
        returns: (batch_size, seq_len, output_dim)
        """
        B, T, _ = x.size()
        if self.norm_type == 'batch':
            x = x.transpose(1, 2)
            x = self.input_norm(x)
            x = x.transpose(1, 2)
        else:
            x = self.input_norm(x)
        x = self.input_proj(x)  # (B, T, D)
        # if self.add_cls:
        #     cls = self.adtrace_proj(aids, sdk_packages, acts, proj_main_channel_ids)
        #     cls = cls.unsqueeze(1).repeat(1, T, 1)
        #     x = torch.cat([cls, x], dim=1)
        
        x = self.positional_encoding(x)  # (B, T, D)
        if self.time_embedding:
            x = self.time_layer(x, ds_list)  # (B, T, D)
        tgt_mask = self.make_tgt_mask(T).to(x.device)

        # if self.adtrace_embedding:
        #     memory = self.adtrace_proj(aids, sdk_packages, acts, proj_main_channel_ids)
        # else:
        #     memory = torch.zeros_like(x)
        memory = torch.zeros_like(x)
        out = self.transformer_decoder(tgt=x, memory=memory, tgt_mask=tgt_mask,
                                       tgt_key_padding_mask=src_key_padding_mask)
        logits = self.output_proj(out)  # (B, T, output_dim)
        # probs = F.softmax(logits, dim=-1)
        return logits
    
    
    def predict_step(self, batch, batch_idx):
        features, labels, masks, ad_ids, ad_ds, cost, revenue = batch
        B, T, D = features.shape
        logits = self(features, ~masks, ad_ds)
        probs = F.softmax(logits, dim=-1)[:, :, 1]
        return {"probs": probs, "logits": logits, "ad_ids": ad_ids, "ad_ds": ad_ds, "cost": cost, "revenue": revenue}
        

    @torch.jit.ignore
    def _compute_roi(self, cost, revenue, preds):
        # cost: (B, T), revenue: (B, T), preds: (B, T)
        B, T = preds.shape
        p = torch.zeros(size=(preds.shape[0], 1)).bool().to(cost.device)
        pad_preds = torch.cat([preds, p], dim=-1)
        first_stops = (~pad_preds).int().argmax(dim=1)  # 预测的停止时间
        time_indices = torch.arange(T).expand(B, T).to(cost.device)
        before_mask = time_indices <= first_stops.unsqueeze(1)
        
        cost_all = cost.sum()
        revenue_all = revenue.sum()
        cost_get = (cost * before_mask).sum()
        revenue_get = (revenue * before_mask).sum()
        cost_discard = cost_all - cost_get
        revenue_discard = revenue_all - revenue_get
        return cost_get, revenue_get, cost_discard, revenue_discard


    @torch.jit.ignore
    def _compute_loss(self, logits, labels, loss_masks, cost):
        # logits: (B, T, output_dim), labels: (B, T), loss_masks: (B, T), cost: (B, T)
        if self.sample_weight is None:
            loss = F.cross_entropy(logits[loss_masks], labels[loss_masks], weight=self.class_weight.to(labels.device))
        elif self.sample_weight == 'log1p':
            loss = F.cross_entropy(logits[loss_masks], labels[loss_masks], reduction='none')
            w1 = self.class_weight.to(labels.device)[labels[loss_masks]]
            loss *= w1
            _w2 = torch.log1p(cost[loss_masks]).to(labels.device)
            loss *= _w2
            loss = loss.mean()
        else:
            raise ValueError(f"sample_weight must be None or 'log1p', but got {self.sample_weight}")
        return loss
        
        
    @torch.jit.ignore 
    def training_step(self, batch, batch_idx):
        # features, labels, masks, ad_ids, ad_ds, cost, revenue, packages, aids, acts, proj_main_channel_ids = batch   # features: (B, T, D), labels: (B, T), masks: (B, T)
        features, labels, masks, ad_ids, ad_ds, cost, revenue = batch   # features: (B, T, D), labels: (B, T), masks: (B, T)
        B, T, D = features.shape
        # logits = self(features, ~masks, ad_ds, packages, aids, acts, proj_main_channel_ids)
        logits = self(features, ~masks, ad_ds)
        probs = F.softmax(logits, dim=-1)[:, :, 1]
        if type(self.pos_curve) == float:
            preds = (probs > self.pos_curve).int()  # (B, T)
        else:
            raise ValueError(f"pos_curve must be a float or a list, but got {type(self.pos_curve)}")
        if not self.mask_observe:  # 如果mask_observe为True，则将mask的头部observe_day个样本置为False，不计算loss
            loss_masks = masks
        else:
            loss_masks = torch.cat([torch.zeros(B, self.observe_day).bool().to(features.device), masks[:, self.observe_day:]], dim=1)
            preds = torch.cat([torch.ones(B, self.observe_day).int().to(features.device), preds[:, self.observe_day:]], dim=1)
        loss = self._compute_loss(logits, labels, loss_masks, cost)
        
        true_positive_ratio = labels[loss_masks].float().mean()  # 正样本比例
        predicted_positive_ratio = (preds[loss_masks] == 1).float().mean()  # 预测为正样本的比例
        accuracy = (preds[loss_masks] == labels[loss_masks]).float().mean() # 准确率
        cost_get, revenue_get, cost_discard, revenue_discard = self._compute_roi(cost, revenue, preds) # 计算ROI
        self.sum_cost_get.update(cost_get)
        self.sum_revenue_get.update(revenue_get)
        self.sum_cost_discard.update(cost_discard)
        self.sum_revenue_discard.update(revenue_discard)
        
        self.log("train/loss", loss)
        self.log("train/true_positive_ratio", true_positive_ratio)
        self.log("train/predicted_positive_ratio", predicted_positive_ratio)
        self.log("train/accuracy", accuracy)
        
        # 可视化每个时间步 t 上的平均概率
        for t in range(10):
            valid_mask = masks[:, t]  # 只看未被 padding 的样本
            if valid_mask.sum() > 0:
                avg_prob = probs[:, t][valid_mask].mean()
                self.log(f'debug/pred_1_at_step_{t}', avg_prob)
        
        return loss
    
    
    @torch.jit.ignore
    def validation_step(self, batch, batch_idx):
        # features, labels, masks, ad_ids, ad_ds, cost, revenue, packages, aids, acts, proj_main_channel_ids = batch
        features, labels, masks, ad_ids, ad_ds, cost, revenue = batch
        B, T, D = features.shape
        # logits = self(features, ~masks, ad_ds, packages, aids, acts, proj_main_channel_ids)
        logits = self(features, ~masks, ad_ds)
        probs = F.softmax(logits, dim=-1)[:, :, 1]
        if type(self.pos_curve) == float:
            preds = (probs > self.pos_curve).int()
        else:
            raise ValueError(f"pos_curve must be a float or a list, but got {type(self.pos_curve)}")
        if not self.mask_observe:  # 如果mask_observe为True，则将mask的头部observe_day个样本置为False，不计算loss
            loss_masks = masks
        else:
            loss_masks = torch.cat([torch.zeros(B, self.observe_day).bool().to(features.device), masks[:, self.observe_day:]], dim=1)
            preds = torch.cat([torch.ones(B, self.observe_day).int().to(features.device), preds[:, self.observe_day:]], dim=1)
        loss = self._compute_loss(logits, labels, loss_masks, cost)
        
        true_positive_ratio = labels[loss_masks].float().mean()  # 正样本比例
        predicted_positive_ratio = (preds[loss_masks] == 1).float().mean()  # 预测为正样本的比例
        accuracy = (preds[loss_masks] == labels[loss_masks]).float().mean() # 准确率
        cost_get, revenue_get, cost_discard, revenue_discard = self._compute_roi(cost, revenue, preds) # 计算ROI
        self.log("val/loss", loss)
        self.log("val/true_positive_ratio", true_positive_ratio)
        self.log("val/predicted_positive_ratio", predicted_positive_ratio)
        self.log("val/accuracy", accuracy)
        return {
            "cost_get": cost_get,
            "revenue_get": revenue_get,
            "cost_discard": cost_discard,
            "revenue_discard": revenue_discard,
        }


    @torch.jit.ignore
    def test_step(self, batch, batch_idx):
        features, labels, masks, ad_ids, ad_ds, cost, revenue = batch
        # features, labels, masks, ad_ids, ad_ds, cost, revenue, packages, aids, acts, proj_main_channel_ids = batch
        B, T, D = features.shape
        logits = self(features, ~masks, ad_ds)
        # logits = self(features, ~masks, ad_ds, packages, aids, acts, proj_main_channel_ids)
        probs = F.softmax(logits, dim=-1)[:, :, 1]
        if type(self.pos_curve) == float:
            preds = (probs > self.pos_curve).int()
        else:
            raise ValueError(f"pos_curve must be a float or a list, but got {type(self.pos_curve)}")
        if self.mask_observe:
            preds = torch.cat([torch.ones(B, self.observe_day).int().to(features.device), preds[:, self.observe_day:]], dim=1)
        cost_get, revenue_get, cost_discard, revenue_discard = self._compute_roi(cost, revenue, preds)  # 计算ROI
        return {
            "cost_get": cost_get,
            "revenue_get": revenue_get,
            "cost_discard": cost_discard,
            "revenue_discard": revenue_discard,
        }
    
       
    @torch.jit.ignore
    def training_epoch_end(self, outputs):
        total_revenue_get = self.sum_revenue_get.compute()
        total_cost_get = self.sum_cost_get.compute()
        total_revenue_discard = self.sum_revenue_discard.compute()
        total_cost_discard = self.sum_cost_discard.compute()
        self.log("train/total_revenue_get", total_revenue_get)
        self.log("train/total_cost_get", total_cost_get)
        self.log("train/roi_get", total_revenue_get / (total_cost_get + 1e-8))
        self.log("train/total_revenue_discard", total_revenue_discard)
        self.log("train/total_cost_discard", total_cost_discard)
        self.log("train/roi_discard", total_revenue_discard / (total_cost_discard + 1e-8))
        self.sum_revenue_get.reset()
        self.sum_cost_get.reset()
        self.sum_revenue_discard.reset()
        self.sum_cost_discard.reset()
        # 记录所有参数的统计量
        for name, param in self.named_parameters():
            if 'weight' in name: 
                self.logger.experiment.add_histogram(
                    f"params/{name}", 
                    param, 
                    self.current_epoch
                )
                if param.grad is not None:
                    self.logger.experiment.add_histogram(
                        f"grads/{name}", 
                        param.grad, 
                        self.current_epoch
                    )
        
    @torch.jit.ignore
    def validation_epoch_end(self, outputs):
        total_revenue_get = sum(output["revenue_get"] for output in outputs)
        total_cost_get = sum(output["cost_get"] for output in outputs)
        total_revenue_discard = sum(output["revenue_discard"] for output in outputs)
        total_cost_discard = sum(output["cost_discard"] for output in outputs)
        self.log("val/total_revenue_get", total_revenue_get)
        self.log("val/total_cost_get", total_cost_get)
        self.log("val/roi_get", total_revenue_get / (total_cost_get + 1e-8))
        self.log("val/total_revenue_discard", total_revenue_discard)
        self.log("val/total_cost_discard", total_cost_discard)
        self.log("val/roi_discard", total_revenue_discard / (total_cost_discard + 1e-8))
        
        
    @torch.jit.ignore
    def test_epoch_end(self, outputs):
        total_revenue_get = sum(output["revenue_get"] for output in outputs)
        total_cost_get = sum(output["cost_get"] for output in outputs)
        total_revenue_discard = sum(output["revenue_discard"] for output in outputs)
        total_cost_discard = sum(output["cost_discard"] for output in outputs)
        self.log("test/total_revenue_get", total_revenue_get)
        self.log("test/total_cost_get", total_cost_get)
        self.log("test/roi_get", total_revenue_get / (total_cost_get + 1e-8))
        self.log("test/total_revenue_discard", total_revenue_discard)
        self.log("test/total_cost_discard", total_cost_discard)
        self.log("test/roi_discard", total_revenue_discard / (total_cost_discard + 1e-8))
        self.log("test/roi_diff", (total_revenue_get / (total_cost_get + 1e-8)) - (total_revenue_discard / (total_cost_discard + 1e-8)))
        

    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=self.lr, weight_decay=self.weight_decay)



class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-torch.log(torch.tensor(10000.0)) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        x = x + self.pe[:, :x.size(1)]
        return x
    
    
class TimeEmbedding(nn.Module):
    def __init__(self, time_model):
        super(TimeEmbedding, self).__init__()
        self.holiday = nn.Embedding(2, time_model)
        self.day_of_week = nn.Embedding(7, time_model)
        self.month = nn.Embedding(12, time_model)
        self.cn_holidays = holidays.CountryHoliday('CN')

    def forward(self, x, ds_list: List[str]):
        B, T, _ = x.shape
        indices = []
        for ds in ds_list:
            day_indices = []
            for j in range(T):
                date_obj = datetime.strptime(ds, "%Y-%m-%d").date() + timedelta(days=j)
                day_indices.append((date_obj.weekday(), date_obj.month - 1, int(date_obj in self.cn_holidays)))
            indices.append(day_indices)
        indices = torch.tensor(indices).to(x.device)
        d_emb = self.day_of_week(indices[:, :, 0])
        m_emb = self.month(indices[:, :, 1])
        h_emb = self.holiday(indices[:, :, 2])
        return torch.concat([x, (d_emb + m_emb + h_emb) / 3], dim=-1)

    
class AdtraceEmbedding(nn.Module):
    def __init__(self, d_model):
        super(AdtraceEmbedding, self).__init__()
        self.ios_android_others = nn.Embedding(3, d_model)  # 苹果、安卓、其他
        self.aid = nn.Embedding(10, d_model)
        self.act = nn.Embedding(200, d_model)
        self.device = 0
        self.sdk_package = nn.Embedding(50, d_model)
        self.proj_main_channel = nn.Embedding(50, d_model)
        # self.proj_sub_channel = nn.Embedding(50, d_model)
        
        
    def forward(self, aids: List[str], acts: List[str], ios_or_android: List[str]):
        raise NotImplementedError
        # B = len(aids)
        # _aids = torch.tensor(aids).to(self.device)
        # _acts = torch.tensor(acts).to(self.device)
        
        # aids_emb = self.aid(_aids).unsqueeze(1)
        # sdk_packages_emb = self.sdk_package(_sdk_packages).unsqueeze(1)
        # acts_emb = self.act(_acts).unsqueeze(1)
        # proj_main_channel_ids_emb = self.proj_main_channel(_proj_main_channel_ids).unsqueeze(1)
        
        # mem_table = torch.cat([aids_emb, sdk_packages_emb, acts_emb, proj_main_channel_ids_emb], dim=1)
        # # print(mem_table.shape)
        # return mem_table


class DeviceEmbedding(nn.Module):
    def __init__(self, d_model):
        super(DeviceEmbedding, self).__init__()
        self.device = nn.Embedding(10, d_model)

    def forward(self, devices: List[str]):
        raise NotImplementedError
        # return self.device(devices).unsqueeze(1)


            
