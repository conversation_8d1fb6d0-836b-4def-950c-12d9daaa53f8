import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

import os
from abc import ABC, abstractmethod
import pymysql
from src.utils.logger import setup_logger
from src.config import AdConfig


class ModelBase(ABC):
    def __init__(self, name: str, class_path: str, log_file_path: str, _config: AdConfig, **kwargs):
        self.name = name
        self.config = _config
        self.log_file_path = log_file_path
        if not os.path.isabs(self.log_file_path):
            self.log_file_path = os.path.join(str(Path(__file__).parent.parent), '..', self.log_file_path)
        self.logger = setup_logger(self.name, self.log_file_path)
        self.logger.info(f"model {name} initialized")
        self.db_name = _config['db_name'] or os.getenv("DB_NAME")
        self.ga_project_id = _config['ga_project_id'] or os.getenv("GA_PROJECT_ID")
    
    
    def update_task_status(self, flag: str):
        """ 更新任务状态 """
        if os.getenv("DB_TYPE") == "sqlite":
            # 使用SQLite数据库
            from sqlalchemy import create_engine
            import pandas as pd
            db_path = os.getenv("DB_PATH", "test_ad_intervention.db")
            engine = create_engine(f'sqlite:///{db_path}')

            # 创建状态记录
            status_df = pd.DataFrame({
                'ds': [self.ds],
                'task_name': [self.name],
                'task_status': [flag],
                'project_id': [self.ga_project_id]
            })
            status_df.to_sql('service_status', engine, if_exists='append', index=False)
            self.logger.info(f"Task status updated to: {flag} (SQLite)")

        elif os.getenv("DB_USER") and os.getenv("DB_PASSWORD") and os.getenv("DB_HOST") and os.getenv("DB_PORT"):
            conn = pymysql.connect(user=os.getenv("DB_USER"), password=os.getenv("DB_PASSWORD"),
                                   host=os.getenv("DB_HOST"), port=int(os.getenv("DB_PORT")), db=self.db_name)

            try:
                with conn.cursor() as cursor:
                    query = f"insert into service_status (ds, task_name, task_status, project_id) values ('{self.ds}', '{self.name}', '{flag}', '{self.ga_project_id}') on duplicate key update task_status = '{flag}'"
                    cursor.execute(query)
                    conn.commit()
                    self.logger.info(f"Task status updated to: {flag} (MySQL)")
            finally:
                conn.close()
        else:
            self.logger.info(f"No database connection configured, task status would be: {flag}")
        
    
    
    def load_task_status(self, ds, task_name) -> str:
        """ 加载任务状态 """
        if os.getenv("DB_TYPE") == "sqlite":
            # 使用SQLite数据库
            from sqlalchemy import create_engine
            import pandas as pd
            db_path = os.getenv("DB_PATH", "test_ad_intervention.db")
            engine = create_engine(f'sqlite:///{db_path}')

            try:
                query = f"SELECT task_status FROM service_status WHERE ds = '{ds}' AND task_name = '{task_name}' AND project_id = '{self.ga_project_id}'"
                result_df = pd.read_sql(query, engine)
                if not result_df.empty:
                    return result_df.iloc[0]['task_status']
                else:
                    return "pending"
            except Exception as e:
                self.logger.info(f"SQLite query failed: {e}, returning 'pending' status")
                return "pending"

        elif os.getenv("DB_USER") and os.getenv("DB_PASSWORD") and os.getenv("DB_HOST") and os.getenv("DB_PORT"):
            conn = pymysql.connect(user=os.getenv("DB_USER"), password=os.getenv("DB_PASSWORD"),
                                   host=os.getenv("DB_HOST"), port=int(os.getenv("DB_PORT")), db=self.db_name)
            try:
                with conn.cursor() as cursor:
                    query = f"select task_status from service_status where ds = '{ds}' and task_name = '{task_name}' and project_id = '{self.ga_project_id}'"
                    cursor.execute(query)
                    result = cursor.fetchone()
                    return result[0] if result else "pending"
            finally:
                conn.close()
        else:
            self.logger.info("No database connection configured, returning 'pending' status")
            return "pending"
        
    
    @abstractmethod
    def run(self):
        pass