
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import json
import os
import pandas as pd
import traceback
from datetime import datetime, timedelta
import pytz
import time
import yaml
import argparse
from tqdm import tqdm
from collections import Counter

import numpy as np
from sqlalchemy import create_engine
import torch
from torch.nn import functional as F
import pytorch_lightning as pl
from typing import List, Tuple
from config import AdConfig
from models.base import ModelBase
from dataset.request import TradeDeskManager
from dataset.seq2seq_loader import AdsDataModule
from models.seq2seq import AdsTransformer


class Seq2SeqDaily(ModelBase):
    def __init__(self, name: str, 
                 class_path: str, log_file_path: str, model_path: str,
                 platform: str, project: str, _config: AdConfig, seq2seq_config_path: str, **kwargs):
        super().__init__(name, class_path, log_file_path, _config, **kwargs)
        self.config = _config
        self.td_manager = TradeDeskManager(self.config)
        self.today = datetime.now(pytz.timezone("Asia/Shanghai")).date()  # 当前日期
        self.ds = kwargs.get("ds", (self.today - timedelta(days=1)).strftime("%Y-%m-%d"))
        if not model_path.startswith('/'):
            project_path = Path(__file__).parent.parent.parent
            self.model_path = os.path.join(project_path, model_path)
        else:
            self.model_path = model_path
        self.platform = platform  # "1": 今日头条, "2": 快手,  "3": 广点通
        self.project = project   # TD上的project字段，用于传入接口获取广告指标 "2": 3D捕鱼, "10353": ue5捕鱼 "10351": urp捕鱼
        self.ga_project_id = _config['ga_project_id'] or os.getenv("GA_PROJECT_ID")  # GA中的项目ID，对应数据库结果表的project_id字段
        self.db_name = _config['db_name'] or os.getenv("DB_NAME")
        assert self.db_name is not None, "db_name is required"
        assert self.platform in ["1", "2", "3"], "platform should be '1', '2' or '3'"
        assert self.project is not None, "project is required"
        assert self.ga_project_id is not None, "ga_project_id is required"
        
        with open(seq2seq_config_path, 'r') as f:
            self.seq2seq_config = yaml.safe_load(f)
        self.data_config = self.seq2seq_config['data']
        
        self.data_config['start_ds_test'] = (datetime.strptime(self.ds, "%Y-%m-%d") - timedelta(days=self.data_config['max_seq_length'] + 10)).strftime("%Y-%m-%d")
        self.data_config['end_ds_test'] = self.ds
        self.data_config['load_from_file'] = False
        self.data_config['platform_id'] = self.platform
        self.data_config['project'] = self.project
        self.data_config['data_dir'] = os.path.dirname(log_file_path)
        # self.data_config['data_dir'] = '/mnt/data/xuyuhong/ad-intervention/data/'
        self.data_config['mode'] = 'eval'
        self.data_config['batch_size'] = 2048
        
    def _load_model(self):        
        model = AdsTransformer.load_from_checkpoint(self.model_path)
        model.eval()
        return model
        # scripted_model = torch.jit.script(model)
        # return scripted_model
    
    
    def output_result(self, stop_ids_and_probs: List[Tuple[str, float]]):
        self.logger.info("saving results...")
        output_df = pd.DataFrame({
            'ad_id': [ad_id for ad_id, _ in stop_ids_and_probs],
            'predicted_proba': [p for _, p in stop_ids_and_probs],
            'adtrace_platform': self.platform,
            "action_taken": "STOP",
            "model_name": self.name,
            "project_id": self.ga_project_id,
            "ds": self.ds
        })
        output_df['action_reason'] = "prob=" + output_df['predicted_proba'].astype(str)
        output_df.drop(columns=['predicted_proba'], inplace=True)
        engine = create_engine('mysql+pymysql://{}:{}@{}:{}/{}'.format(
            os.getenv("DB_USER"), os.getenv("DB_PASSWORD"), os.getenv("DB_HOST"), int(os.getenv("DB_PORT")), self.db_name,
        ))
        output_df.to_sql('ad_intervention_history', engine, if_exists='append', index=False)
            
    
    def run(self):
        start_time = time.time()
        # 如果已经运行过则跳过
        if self.load_task_status(self.ds, self.name) != "pending":
            self.logger.info(f"{self.name} already run, skip")
            return {
                "status": "skip",
                "info": f"{self.ga_project_id}: {self.name} already run, skip",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            
        try:
            # 模型加载
            model = self._load_model()
            self.logger.info(f"load model success, ckpt: {self.model_path}")
            data_module = AdsDataModule(**self.data_config, global_config=self.config)
            data_module.setup()
            
            # 执行预测
            stop_ids_and_probs = []
            trainer = pl.Trainer(
                accelerator="cpu",
            )
            preds = trainer.predict(model, data_module)
            for item in tqdm(preds):
                ad_ids, ad_ds, cost, revenue, probs = item['ad_ids'], item['ad_ds'], item['cost'], item['revenue'], item['probs']
                for i in range(len(ad_ids)):
                    day_delta = (datetime.strptime(self.ds, "%Y-%m-%d") - datetime.strptime(ad_ds[i], "%Y-%m-%d")).days
                    if day_delta > self.data_config['max_seq_length'] // 2 or day_delta < 2:
                        continue
                    p = probs[i][day_delta]
                    if p < 0.3:
                        stop_ids_and_probs.append((ad_ids[i], p))
            
            # 输出结果
            platform_name = 'jrtt' if self.platform == '1' else 'kuaishou' if self.platform == '2' else 'gdt'
            unpaused_ad_ids = self.td_manager.get_unpaused_ad_ids(platform=platform_name, project_id=int(self.project))
            stop_ids_and_probs = [(ad_id, p) for ad_id, p in stop_ids_and_probs if str(ad_id) in unpaused_ad_ids]
            self.output_result(stop_ids_and_probs)
            
             # 更新任务状态
            self.update_task_status(flag='success')
            
            # 收集执行结果
            result = {
                "status": "success",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
                "info": f"ga_project_id: {self.ga_project_id}, platform: {platform_name}, model: {self.name}, ds: {self.ds}, len(stop_ads): {len(stop_ids_and_probs)}, len(candidate_ads): {len(unpaused_ad_ids)}",
            }
            self.logger.info(result)
            
        except Exception as e:
            self.logger.error(f"ga_project_id: {self.ga_project_id}, pipeline {self.name} failed: {str(e)}", exc_info=True)
            exc_type, exc_value, exc_traceback = sys.exc_info()
            tb = traceback.extract_tb(exc_traceback)[-1] 
            result = {
                "status": "failed",
                "error": str(e),
                "error_line": f'{tb.filename}:{tb.lineno}',
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            self.update_task_status(flag='failure')
            
        return result
    
    
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, required=True, help='配置文件')
    parser.add_argument('--env', type=str, default="", help='环境配置')
    parser.add_argument('--ds', type=str, default=(datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"))
    args = parser.parse_args()
    # python src/models/seq2seq_daily.py --config configs/config-prod-3d.yaml --env .env.prod
    
    model = Seq2SeqDaily(name="seq2seq_v20250707", 
                        class_path="models.seq2seq_daily", 
                        log_file_path="logs/seq2seq_daily.log", 
                        model_path="/mnt/data/xuyuhong/ad-intervention/models/v3-0707-1035/checkpoints/epoch=55-step=7168.ckpt", 
                        platform="1", 
                        project="2", 
                        ds=args.ds,
                        _config=AdConfig(config_path=args.config, env_path=args.env),
                        seq2seq_config_path="/mnt/data/xuyuhong/ad-intervention/configs/seq2seq_config_v20250707.yaml")
    result = model.run()


        
