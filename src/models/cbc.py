import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import json
import os
import pandas as pd
import traceback
from datetime import datetime, timedelta
import pytz
from typing import List, Tuple
import time

import numpy as np
from sqlalchemy import create_engine
from catboost import CatBoostClassifier, CatBoostRegressor

from config import AdConfig
from dataset.request import TradeDeskManager
from models.base import ModelBase


class CBC(ModelBase):
    def __init__(self, name: str, 
                 class_path: str, log_file_path: str, model_path: str,
                 platform: str, project: str, _config: AdConfig, **kwargs):
        super().__init__(name, class_path, log_file_path, _config, **kwargs)
        self.config = _config
        self.td_manager = TradeDeskManager(self.config)
        self.today = datetime.now(pytz.timezone("Asia/Shanghai")).date()  # 当前日期
        self.ds = kwargs.get("ds", (self.today - timedelta(days=1)).strftime("%Y-%m-%d"))
        if not model_path.startswith('/'):
            project_path = Path(__file__).parent.parent.parent
            self.model_path = os.path.join(project_path, model_path)
        else:
            self.model_path = model_path
        self.platform = platform  # "1": 今日头条, "2": 快手,  "3": 广点通
        self.project = project   # TD上的project字段，用于传入接口获取广告指标 "2": 3D捕鱼, "10353": ue5捕鱼 "10351": urp捕鱼
        self.ga_project_id = _config['ga_project_id'] or os.getenv("GA_PROJECT_ID")  # GA中的项目ID，对应数据库结果表的project_id字段
        self.db_name = _config['db_name'] or os.getenv("DB_NAME")
        assert self.db_name is not None, "db_name is required"
        assert self.platform in ["1", "2", "3"], "platform should be 1, 2 or 3"
        assert self.project is not None, "project is required"
        assert self.ga_project_id is not None, "ga_project_id is required"
        
    def _load_model(self):        
        with open(f"{self.model_path}_metadata.json", "r") as f:
            metadata = json.load(f)
        model = CatBoostClassifier()
        model.load_model(f"{self.model_path}.cbm")
        return model, metadata
    
    
    def _save_model(self, model, filepath, train_X, params):
        model.save_model(f"{filepath}.cbm")
        # 保存特征元数据
        metadata = {
            "feature_names": model.feature_names_,
            "feature_types": [str(train_X[col].dtype) for col in train_X.columns],
            "params": params,
        }
        print(metadata)
        with open(f"{filepath}_metadata.json", "w") as f:
            json.dump(metadata, f)
        
    
    def _calc_growth(self, class_costs, neg_cost, pos_cost, neg_roi, base_roi=0.08):
        growth = class_costs[2] / sum(class_costs) * neg_cost / (pos_cost + neg_cost) * (base_roi - neg_roi) / neg_roi
        # print(f"growth: {growth: .4f}")
        return growth
        
        
    def split_ad(self, td_df: pd.DataFrame, observe_day: int, feature_start_ds: str, feature_end_ds: str=None, mode: str='train'):
        """ 
        td_df: 广告数据
        observe_day: 观测周期（天）
        feature_start_ds, feature_end_ds: 广告创建开始日期，结束日期。该范围内的广告才作为数据集
        mode: 模式 train或eval
        根据广告创建时间和观测期长度，将广告分为三类
        """
        td_df['dt'] = pd.to_datetime(td_df['date_start'])
        td_df['create_ds'] = td_df.groupby('ad_id')['date_start'].transform('min')
        td_df['create_dt'] = pd.to_datetime(td_df['create_ds'])
        td_df['delay'] = (td_df['dt'] - td_df['create_dt']).dt.days
        td_df['max_delay'] = td_df.groupby('ad_id')['delay'].transform('max')
        
        assert mode in ['train', 'eval']
        if mode == 'eval':
            # 线上推理阶段每日例行，只保留创建日期为feature_start_ds的广告
            valid_ad = td_df[td_df['create_dt'] == pd.to_datetime(feature_start_ds)]  
            # print(f"valid_ad.shape: {valid_ad.shape}")
            # print(f"valid_ad_dead.shape: {valid_ad_dead.shape}")
            return None, valid_ad, None
        
        else:
            if feature_end_ds is None:
                valid_ad = td_df[td_df['create_dt'] >= pd.to_datetime(feature_start_ds)]
            else:
                valid_ad = td_df[(td_df['create_dt'] >= pd.to_datetime(feature_start_ds)) & (td_df['create_dt'] <= pd.to_datetime(feature_end_ds))]
            valid_ad_dead = valid_ad[valid_ad['max_delay'] < observe_day]
            valid_ad_survive_observe = valid_ad[(valid_ad['max_delay'] >= observe_day) & (valid_ad['delay'] < observe_day)]
            valid_ad_survive_unknown = valid_ad[(valid_ad['max_delay'] >= observe_day) & (valid_ad['delay'] >= observe_day)]
            print(f"valid_ad.shape: {valid_ad.shape}")
            print(f"valid_ad_dead.shape: {valid_ad_dead.shape}")
            print(f"valid_ad_survive_observe.shape: {valid_ad_survive_observe.shape}")
            print(f"valid_ad_survive_unknown.shape: {valid_ad_survive_unknown.shape}")
            
            result_dead = valid_ad_dead.groupby('ad_id').agg(
                    total_pay1=('pay1', 'sum'),
                    total_pay3=('pay3', 'sum'),
                    total_pay7=('pay7', 'sum'),
                    total_pay30=('pay30', 'sum'),
                    total_pay180=('pay180', 'sum'),
                    total_cost=('cost', 'sum')
                ).assign(
                    pay180_over_cost=lambda x: x['total_pay180'] / (x['total_cost'] + 1e-9)
                ).reset_index()
            
            result_survive_observe = valid_ad_survive_observe.groupby('ad_id').agg(
                    total_pay1=('pay1', 'sum'),
                    total_pay3=('pay3', 'sum'),
                    total_pay7=('pay7', 'sum'),
                    total_pay30=('pay30', 'sum'),
                    total_pay180=('pay180', 'sum'),
                    total_cost=('cost', 'sum')
                ).assign(
                    pay180_over_cost=lambda x: x['total_pay180'] / (x['total_cost'] + 1e-9)
                ).reset_index()

            result_survive_unknown = valid_ad_survive_unknown.groupby('ad_id').agg(
                    total_pay1=('pay1', 'sum'),
                    total_pay3=('pay3', 'sum'),
                    total_pay7=('pay7', 'sum'),
                    total_pay30=('pay30', 'sum'),
                    total_pay180=('pay180', 'sum'),
                    total_cost=('cost', 'sum')
                ).assign(
                    pay180_over_cost=lambda x: x['total_pay180'] / (x['total_cost'] + 1e-9)
                ).reset_index()

            result_all = valid_ad.groupby('ad_id').agg(
                    total_pay1=('pay1', 'sum'),
                    total_pay3=('pay3', 'sum'),
                    total_pay7=('pay7', 'sum'),
                    total_pay30=('pay30', 'sum'),
                    total_pay180=('pay180', 'sum'),
                    total_cost=('cost', 'sum')
                ).assign(
                    pay180_over_cost=lambda x: x['total_pay180'] / (x['total_cost'] + 1e-9)
                ).reset_index()
            for name, target in [("all", result_all), ("dead", result_dead), ("survive_observe", result_survive_observe), ("survive_unknown", result_survive_unknown)]:
                print(f"#{name}: {target.shape[0]}")
                print(f"\tcost: {target['total_cost'].sum(): .2f}")
                print(f"\t1 ROI: {target['total_pay1'].sum() / target['total_cost'].sum(): .4f}")
                print(f"\t3 ROI: {target['total_pay3'].sum() / target['total_cost'].sum(): .4f}")
                print(f"\t7 ROI: {target['total_pay7'].sum() / target['total_cost'].sum(): .4f}")
                print(f"\t30 ROI: {target['total_pay30'].sum() / target['total_cost'].sum(): .4f}")
                print(f"\t180 ROI: {target['total_pay180'].sum() / target['total_cost'].sum(): .4f}")
            return valid_ad_dead, valid_ad_survive_observe, valid_ad_survive_unknown
        

    
    def generate_feature(self, ad_df, ad_label_df, observe_day, label_range, label_day, roi_threshold, cost_threshold):

        dfs = []
        for day in range(0, observe_day):
            basic_features = ["cost", "show","click","cost_by_thousand_show","new_user","new_paid_user","new_user_cost","new_paid_user_cost", "cvr","ctr","convert","active_cost","convert_cost","active"]
            pay_features = ["pay1","pay2","pay3","pay4","pay5","pay6","pay7"][:observe_day - day]
            ltv_features = ["ltv1", "ltv2","ltv3","ltv4","ltv5","ltv6","ltv7"][:observe_day - day]
            roi_features = ["roi1","roi2","roi3","roi4","roi5","roi6","roi7"][:observe_day - day]
            stay_features=["stay_num2","stay_num3","stay_num4","stay_num5","stay_num6","stay_num7"][:observe_day - day - 1]
            adtrace_features =["new_user_ad_trace","pay1_ad_trace","pay7_ad_trace"] if observe_day - day == 7 else ["new_user_ad_trace","pay1_ad_trace"]
            # LTV预测、流失预测模型输出结果作为特征
            p_type = 0
            pay_features_future = ['pay7', 'pay15', 'pay30', 'pay90', 'pay180', 'total_pay'][:p_type]
            ltv_features_future = ["ltv7","ltv15","ltv30","ltv90","ltv180"][:p_type]
            roi_features_future = ["roi7","roi15","roi30","roi90","roi180"][:p_type]
            stay_features_future = ["stay_num7","stay_num15","stay_num30","stay_num90","stay_num180"][:p_type]
            
            merge_features = basic_features + pay_features + ltv_features + roi_features + stay_features + adtrace_features + pay_features_future + ltv_features_future + roi_features_future + stay_features_future
            day_df = ad_df[ad_df['delay'] == day][['ad_id'] + merge_features].copy()
            day_df.columns = ['ad_id'] + [f'day{day}.{cols}' for cols in merge_features]
            dfs.append(day_df)
            
        merged_df = dfs[0]
        for df in dfs[1:]:
            merged_df = pd.merge(merged_df, df, on='ad_id', how='outer')
        merged_df.fillna(0, inplace=True)
        
        if 'platform_id' in ad_df.columns:
            ad_id_platform = ad_df[['ad_id', 'platform_id']].drop_duplicates('ad_id')
            merged_df = pd.merge(merged_df, ad_id_platform, on='ad_id', how='left')

        self.logger.info(merged_df.head(1).to_string())
        self.logger.info(f"X.shape: {merged_df.shape}")

        if ad_label_df is not None:
            valid_ad_label_df = ad_label_df[(ad_label_df['delay'] >= observe_day) & (ad_label_df['delay'] < observe_day + label_range)]
            pay_cost_df = valid_ad_label_df.groupby('ad_id')[[f'pay{label_day}', 'cost']].sum()
            pay_cost_df.columns = ['future_pay', 'future_cost']
            pay_cost_df['future_roi'] = pay_cost_df['future_pay'] / pay_cost_df['future_cost']
            pay_cost_df['label'] = (pay_cost_df['future_roi'] >= roi_threshold) & (pay_cost_df['future_cost'] >= cost_threshold)
            # print("y.shape:", pay_cost_df.shape)
            # print("y_pos.shape:", pay_cost_df[pay_cost_df['label'] == True].shape)
            # print("y_neg.shape:", pay_cost_df[pay_cost_df['label'] == False].shape)
            pay_cost_df.reset_index(inplace=True)
            self.logger.info(pay_cost_df.head(20).to_string())
            return merged_df, pay_cost_df  
        
        return merged_df, None
        
        
    def output_result(self, stop_ids_and_probs: List[Tuple[str, float]], params: dict):
        self.logger.info("saving results...")
        output_df = pd.DataFrame({
            'ad_id': [ad_id for ad_id, _ in stop_ids_and_probs],
            'predicted_proba': [p for _, p in stop_ids_and_probs],
            'adtrace_platform': self.platform,
            "action_taken": "STOP",
            "model_name": self.name,
            "project_id": self.ga_project_id,
            "ds": self.ds
        })
        output_df['action_reason'] = "prob=" + output_df['predicted_proba'].astype(str)
        output_df['action_params'] = f'observe_day={params["observe_day"]}'
        output_df.drop(columns=['predicted_proba'], inplace=True)

        # 检查数据库连接配置
        if os.getenv("DB_TYPE") == "sqlite":
            # 使用SQLite数据库
            db_path = os.getenv("DB_PATH", "test_ad_intervention.db")
            engine = create_engine(f'sqlite:///{db_path}')
            output_df.to_sql('ad_intervention_history', engine, if_exists='append', index=False)
            self.logger.info(f"Successfully saved {len(output_df)} records to SQLite database: {db_path}")
        elif os.getenv("DB_USER") and os.getenv("DB_PASSWORD") and os.getenv("DB_HOST") and os.getenv("DB_PORT"):
            # 使用MySQL数据库
            engine = create_engine('mysql+pymysql://{}:{}@{}:{}/{}'.format(
                os.getenv("DB_USER"), os.getenv("DB_PASSWORD"), os.getenv("DB_HOST"), int(os.getenv("DB_PORT")), self.db_name,
            ))
            output_df.to_sql('ad_intervention_history', engine, if_exists='append', index=False)
            self.logger.info(f"Successfully saved {len(output_df)} records to MySQL database")
        else:
            self.logger.info("No database connection configured, printing results:")
            self.logger.info(f"Results DataFrame:\n{output_df.to_string()}")
            # 保存到本地文件作为备份
            output_file = f"results_{self.name}_{self.ds}.csv"
            output_df.to_csv(output_file, index=False)
            self.logger.info(f"Results saved to local file: {output_file}")
               
    
    def run(self):
        start_time = time.time()
        # 如果已经运行过则跳过
        if self.load_task_status(self.ds, self.name) != "pending":
            self.logger.info(f"{self.name} already run, skip")
            return {
                "status": "skip",
                "info": f"{self.ga_project_id}: {self.name} already run, skip",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            
        try:
            # 模型加载
            model, metadata = self._load_model()
            params = metadata['params']
            self.logger.info(f"load model success, params: {params}")
            
            # 数据加载
            self.logger.info("data loading...")
            feature_columns = ["cost", "show","click","cost_by_thousand_show","new_user","new_paid_user","new_user_cost","new_paid_user_cost",
                            "ltv1","roi1","cvr","ctr","convert","active_cost","convert_cost","active","ad_id_total",
                            "pay1","pay2","pay3","pay4","pay5","pay6","pay7","pay15","pay30","pay180",
                            "ltv2","ltv3","ltv4","ltv5","ltv6","ltv7","ltv15","ltv30","ltv180",
                            "roi2","roi3","roi4","roi5","roi6","roi7","roi15","roi30","roi90","roi180",
                            "stay_num2","stay_num3","stay_num4","stay_num5","stay_num6","stay_num7","stay_num15","stay_num30","stay_num90","stay_num180",
                            "ltv90","pay90","new_user_ad_trace","pay1_ad_trace","pay7_ad_trace", "total_pay"]
            td_data = {}
            start_ds = (datetime.strptime(self.ds, '%Y-%m-%d') - timedelta(days=params['observe_day'] + 10)).strftime('%Y-%m-%d')
            create_ds = (datetime.strptime(self.ds, '%Y-%m-%d') - timedelta(days=params['observe_day'] - 1)).strftime('%Y-%m-%d')
            end_ds = self.ds
            # 检查是否有完整的TD配置，如果没有则使用模拟数据
            if (not os.getenv("TD_URL") or not os.getenv("TD_AUTH_URL") or
                not os.getenv("TD_USERNAME") or not os.getenv("TD_PASSWORD") or
                os.getenv("TD_URL") == "http://test.com"):
                self.logger.info("No valid TD_URL configured, using mock data for testing...")
                # 创建模拟数据
                for ds in pd.date_range(start_ds, end_ds):
                    ds_str = ds.strftime('%Y-%m-%d')
                    # 创建一些模拟的广告数据
                    mock_data = pd.DataFrame({
                        'ad_id': [f'mock_ad_{j}' for j in range(5)],  # 5个模拟广告
                        'date_start': [ds_str] * 5,
                        'cost': [100 + j * 10 for j in range(5)],
                        'show': [1000 + j * 100 for j in range(5)],
                        'click': [50 + j * 5 for j in range(5)],
                        'new_user': [10 + j for j in range(5)],
                        'convert': [5 + j for j in range(5)],
                        'active': [20 + j * 2 for j in range(5)],
                        'pay1': [j * 2 for j in range(5)],
                        'pay2': [j * 3 for j in range(5)],
                        'pay3': [j * 4 for j in range(5)],
                        'pay4': [j * 5 for j in range(5)],
                        'pay5': [j * 6 for j in range(5)],
                        'pay6': [j * 7 for j in range(5)],
                        'pay7': [j * 8 for j in range(5)],
                    })
                    # 添加其他必要的列
                    for col in feature_columns:
                        if col not in mock_data.columns:
                            mock_data[col] = 0.0
                    td_data[ds] = mock_data
            else:
                for ds in pd.date_range(start_ds, end_ds):
                    one_day_df = self.td_manager.fetch_td_data_without_download_to_db(ds, ds,
                                                                                      column_list=feature_columns, max_retries=10,
                                                                                      platform=self.platform, project=self.project)
                    td_data[ds] = one_day_df

            self.logger.info("data preprocessing...")
            _, ad_survive_observe, _ = self.split_ad(pd.concat([td_data[ds] for ds in td_data]), 
                                                     observe_day=params['observe_day'], 
                                                     feature_start_ds=create_ds, 
                                                     mode='eval')
            features_df, _ = self.generate_feature(ad_survive_observe, None, 
                                                   observe_day=params['observe_day'], 
                                                   label_range=params['label_range'], 
                                                   label_day=params['label_day'], 
                                                   roi_threshold=params['roi_threshold'], 
                                                   cost_threshold=params['cost_threshold'])
            ids = features_df['ad_id']
            X = features_df.drop(columns=['ad_id'])
            
            # 执行预测
            self.logger.info("predicting...")
            y_pred_proba = model.predict_proba(X)[:, 1]
            y_pred = model.predict(X)
            
            # 结果保存
            platform_name = 'jrtt' if self.platform == '1' else 'kuaishou' if self.platform == '2' else 'gdt'
            unpaused_ad_ids = self.td_manager.get_unpaused_ad_ids(platform=platform_name, project_id=int(self.project))
            stop_ids_and_probs = []
            for i in range(len(ids)):
                ad_id, prob, pred = ids[i], y_pred_proba[i], y_pred[i]
                if ('predict_threshold' in params and prob < params['predict_threshold']) \
                    or ('predict_threshold' not in params and pred == 0):
                    if str(ad_id) in unpaused_ad_ids:
                        stop_ids_and_probs.append((ad_id, prob))
            self.output_result(stop_ids_and_probs=stop_ids_and_probs, params=params)
            
             # 更新任务状态
            self.update_task_status(flag='success')
            
            # 收集执行结果
            result = {
                "status": "success",
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
                "info": f"ga_project_id: {self.ga_project_id}, platform: {platform_name}, model: {self.name}, ds: {self.ds}, len(stop_ads): {len(stop_ids_and_probs)}, len(candidate_ads): {len(ids)}, len(all_ads): {len(pd.concat([td_data[ds] for ds in td_data]))}",
            }
            self.logger.info(result)
            
        except Exception as e:
            self.logger.error(f"ga_project_id: {self.ga_project_id}, pipeline {self.name} failed: {str(e)}", exc_info=True)
            exc_type, exc_value, exc_traceback = sys.exc_info()
            tb = traceback.extract_tb(exc_traceback)[-1] 
            result = {
                "status": "failed",
                "error": str(e),
                "error_line": f'{tb.filename}:{tb.lineno}',
                "execution_time": str(timedelta(seconds=time.time() - start_time)),
            }
            self.update_task_status(flag='failure')
            
        return result


        
