import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

import os
from abc import ABC, abstractmethod
from src.utils.logger import setup_logger

class RuleBase(ABC):
    def __init__(self, name: str, class_path: str, log_file_path: str, **kwargs):
        self.name = name
        self.log_file_path = log_file_path
        if not os.path.isabs(self.log_file_path):
            self.log_file_path = os.path.join(project_root, '..', self.log_file_path)
        self.logger = setup_logger(self.name, self.log_file_path)
        self.logger.info(f"rule {name} initialized")
    
    @abstractmethod
    def run(self):
        pass

