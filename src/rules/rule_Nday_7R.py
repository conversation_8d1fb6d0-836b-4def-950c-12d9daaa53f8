import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import json
import pymysql
import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime, timedelta
import pytz

from dataset.request import run_fetch_td_data_daily
from rules.base import RuleBase


def get_ad_ltv_data(attribution_day, observed_days, predicted_days, model_name):
    try:
        conn = pymysql.connect(**config['mysql'])

        # SQL查询
        query = """
        SELECT 
            channel_ty_adgroup_id,
            attribution_day,
            observed_days,
            predicted_days,
            pltv,
            user_count,
            record_time,
            record_partition,
            model_name
        FROM 
            ad_ltv_prediction
        WHERE 
            attribution_day = %s 
            AND observed_days = %s 
            AND predicted_days = %s 
            AND adtrace_platform = '1'
            AND model_name = %s
        """

        df = pd.read_sql(query, conn, params=[str(attribution_day), observed_days, predicted_days, model_name])
        conn.close()

        return df

    except Exception as e:
        print(f"Error：{e}")
        return None



def get_ad_profile_data(date_start, date_end):
    try:
        conn = pymysql.connect(**config['mysql'])

        # SQL查询
        query = """
        SELECT 
            ad_id,
            date_start,
            cost,
            new_user
        FROM 
            ad_profile
        WHERE 
            date_start = %s 
            AND date_end = %s
            AND date_update = %s
        ORDER BY 
            date_update
        """

        df = pd.read_sql(query, conn, params=[date_start, date_end, datetime.now().date()])
        conn.close()

        return df

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        return None
    
    
    
class RuleNday7R(RuleBase):
    def __init__(self, name: str, class_path: str, log_file_path: str, **kwargs):
        super().__init__(name, class_path, log_file_path, **kwargs)
        self.ds = kwargs.get("ds")
        # self.attribution_day = kwargs.get("attribution_day")
        self.observed_days = kwargs.get("observed_days")
        self.predicted_days = kwargs.get("predicted_days")
        self.model_name = kwargs.get("model_name", "cbm_v20250416")
        self.prob = kwargs.get("prob", 0.01)
        
        if self.observed_days is None or not isinstance(self.observed_days, int) \
            or self.predicted_days is None or not isinstance(self.predicted_days, int):
            raise ValueError("observed_days and predicted_days must be provided and must be integers")
        
        if self.ds is None:
            self.ds = (datetime.now(pytz.timezone("Asia/Shanghai")) - timedelta(days=1)).strftime("%Y-%m-%d")
        self.attribution_day = (datetime.strptime(self.ds, "%Y-%m-%d") - timedelta(days=self.observed_days-1)).date()
        # if self.attribution_day is None:
            # self.attribution_day = (datetime.now(pytz.timezone("Asia/Shanghai")) - timedelta(days=self.observed_days)).date()
        # else:
            # self.attribution_day = datetime.strptime(self.attribution_day, "%Y-%m-%d").date()
    

    def run(self):
        """连续N天7R=0的腰部广告，则以prob的概率关停"""
        # assert model_name in valid_model_names, f"Invalid model name: {model_name}. Valid names are: {valid_model_names}"
        run_flag = run_fetch_td_data_daily(attribution_day=self.attribution_day, observed_days=self.observed_days, predicted_days=self.predicted_days)
        if run_flag == 1:
            self.logger.info(f"Fetch TD data failed, skip rule {self.name}")
            return

        date_end = self.attribution_day + timedelta(days=self.observed_days-1) # 表示最后观察的是哪一天的数据

        ad_ltv_df = get_ad_ltv_data(self.attribution_day, self.observed_days, self.predicted_days, self.model_name)
        ad_profile_df_daily = get_ad_profile_data(date_end, date_end)
        ad_profile_df_daily_7days = pd.concat(
            [get_ad_profile_data(self.attribution_day-timedelta(days=day), self.attribution_day-timedelta(days=day))
            for day in range(7)])
        ad_profile_df_monthly = get_ad_profile_data(date_end - timedelta(days=30), date_end)

        # 获取每个广告的投放时间
        ad_days_dict = ad_profile_df_daily_7days.groupby(['ad_id'])['date_start'].min().to_dict()
        ad_profile_df_daily['ad_date'] = ad_profile_df_daily['ad_id'].map(ad_days_dict)
        ad_profile_df_daily['date_start'] = pd.to_datetime(ad_profile_df_daily['date_start'])
        ad_profile_df_daily['ad_date'] = pd.to_datetime(ad_profile_df_daily['ad_date'])
        ad_profile_df_daily['n_days'] = (
                    ad_profile_df_daily['date_start'] - ad_profile_df_daily['ad_date']).dt.days + 1
        ad_profile_df_daily = ad_profile_df_daily[ad_profile_df_daily['n_days'] >= 3]

        # 获取当日的腰部广告和尾部广告
        ad_profile_df_daily = ad_profile_df_daily.sort_values(by=['cost'], ascending=[False])
        tail_index = ad_profile_df_daily.shape[0]
        head_index = int(tail_index * 0.1)
        mid_index = int(tail_index * 0.4)

        mid_df_daily = ad_profile_df_daily.iloc[head_index:mid_index]
        tail_df_daily = ad_profile_df_daily.iloc[mid_index:]
        n_shutdown_mid = int(0.1 * tail_index)
        n_shutdown_tail = int(0.3 * tail_index)

        # 获取当月的头部广告
        ad_profile_df_monthly = ad_profile_df_monthly.sort_values(by=['cost'], ascending=[False])
        head_df_monthly = ad_profile_df_monthly.iloc[:int(ad_profile_df_monthly.shape[0]*0.02)]  # 0.02 and 0.1

        # 筛选出真正的腰部广告和尾部广告
        head_monthly_ads = head_df_monthly['ad_id'].tolist()
        mid_df_daily = mid_df_daily[~mid_df_daily['ad_id'].isin(head_monthly_ads)]
        tail_df_daily = tail_df_daily[~tail_df_daily['ad_id'].isin(head_monthly_ads)]

        # 根据ltv预测结果关停腰部广告
        pltv_dict = ad_ltv_df.set_index('channel_ty_adgroup_id')['pltv'].to_dict()
        model_dict = ad_ltv_df.set_index('channel_ty_adgroup_id')['model_name'].to_dict()

        # 查看ad_id重叠部分
        # ad_profile_df_daily_ids = ad_profile_df_daily['ad_id'].tolist()
        # ad_ltv_df_ids = ad_ltv_df['channel_ty_adgroup_id'].tolist()
        # ad_ltv_ids_not_in_ad_profile = ad_ltv_df[~ad_ltv_df['channel_ty_adgroup_id'].isin(ad_profile_df_daily_ids)]['channel_ty_adgroup_id'].tolist()

        mid_df_daily['pltv'] = mid_df_daily['ad_id'].map(pltv_dict).fillna(0)
        mid_df_daily['model_name'] = mid_df_daily['ad_id'].map(model_dict).fillna(self.model_name)
        mid_df_daily_shutdown = mid_df_daily.sort_values(by='pltv', ascending=True).head(n_shutdown_mid)

        ad_intervention_history_df_mid = mid_df_daily_shutdown[['ad_id', 'model_name']]
        ad_intervention_history_df_mid['ds'] = self.ds
        ad_intervention_history_df_mid['action_taken'] = 'STOP'
        ad_intervention_history_df_mid['action_params'] = ''
        ad_intervention_history_df_mid['action_reason'] = mid_df_daily_shutdown.apply(
            lambda row: json.dumps({
                "reason": "Low pLTV mid ads",
                "pLTV": float(row['pltv']),
                "cost": float(row['cost'])
            }), axis=1
        )
        ad_intervention_history_df_mid['executed_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 随机关停N条的尾部广告
        tail_df_daily_shutdown = tail_df_daily.sample(n=n_shutdown_tail, random_state=1)
        ad_intervention_history_df_tail = tail_df_daily_shutdown[['ad_id']]
        ad_intervention_history_df_tail['model_name'] = self.model_name
        ad_intervention_history_df_tail['ds'] = self.ds
        ad_intervention_history_df_tail['action_taken'] = 'STOP'
        ad_intervention_history_df_tail['action_params'] = ''
        ad_intervention_history_df_tail['action_reason'] = tail_df_daily_shutdown.apply(
            lambda row: json.dumps({
                "reason": "Random stop tail ads",
                "cost": float(row['cost'])
            }), axis=1
        )
        ad_intervention_history_df_tail['executed_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 合并腰部和尾部关停广告并上传数据库
        ad_intervention_history_df = pd.concat([ad_intervention_history_df_mid, ad_intervention_history_df_tail], ignore_index=True)
        engine = create_engine('mysql+pymysql://{}:{}@{}:{}/{}'.format(
            config['mysql']['user'], config['mysql']['password'], config['mysql']['host'], config['mysql']['port'], config['mysql']['database']
        ))
        ad_intervention_history_df.to_sql('ad_intervention_history', engine, if_exists='append', index=False)
