import sys
import pathlib
sys.path.append(str(pathlib.Path(__file__).parent.parent))

import os
import csv
import pickle
import re
import yaml
from pprint import pprint
from tqdm import tqdm
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Dict, <PERSON><PERSON>, List
import pandas as pd
import pymysql
from impala.dbapi import connect

from config import AdConfig
from dataset.request import TradeDeskManager


def fetch_all_data(start_ds, end_ds, 
                   platform: str = '1',
                   project: str = '2',
                   config_file: str = None):
    """从TD上获取数据"""
    feature_columns = ["cost", "show","click","cost_by_thousand_show","new_user","new_paid_user","new_user_cost","new_paid_user_cost",
                       "ltv1","roi1","cvr","ctr","convert","active_cost","convert_cost","active","ad_id_total",
                       "pay1","pay2","pay3","pay4","pay5","pay6","pay7","pay15","pay30","pay180",
                       "ltv2","ltv3","ltv4","ltv5","ltv6","ltv7","ltv15","ltv30","ltv180",
                       "roi2","roi3","roi4","roi5","roi6","roi7","roi15","roi30","roi90","roi180",
                       "stay_num2","stay_num3","stay_num4","stay_num5","stay_num6","stay_num7","stay_num15","stay_num30","stay_num90","stay_num180",
                       "ltv90","pay90","new_user_ad_trace","pay1_ad_trace","pay7_ad_trace", "total_pay"]
    tmp_data = {}
    td_manager = TradeDeskManager(config_file)
    for ds in tqdm(pd.date_range(start_ds, end_ds)):
        df = td_manager.fetch_td_data_without_download_to_db(ds, ds, feature_columns, max_retries=10, platform=platform, project=project)
        tmp_data[ds] = df
    return tmp_data


def calc_growth(class_costs, neg_cost, pos_cost, neg_roi, base_roi=0.08):
    growth = class_costs[2] / sum(class_costs) * neg_cost / (pos_cost + neg_cost) * (base_roi - neg_roi) / neg_roi
    return growth


def count_affect_ads(start_ds, end_ds, account_create: Dict[int, str]):
    """ 统计时间段内关停的广告数 
    account_create: 全托管账号归属的日期
    """
    td_data = fetch_all_data((datetime.strptime(start_ds, '%Y-%m-%d') - timedelta(days=3)).strftime('%Y-%m-%d'), end_ds)
    df = pd.concat(td_data.values())
    ad2account = df.groupby('ad_id')['account_id'].first().to_dict()
    print(ad2account)
    
    affect_ads = defaultdict(set)
    result_df = query_intervention_result(start_ds, end_ds, prod=True)
    for row in result_df.itertuples():
        account_id = ad2account.get(int(row.ad_id))
        if row.action_taken == 'STOP' and account_id in account_create and row.ds.strftime('%Y-%m-%d') >= account_create[account_id]:
            affect_ads[row.ds.strftime('%Y-%m-%d')].add(int(row.ad_id))
    sum_affect_ads = 0
    for ds in affect_ads:
        sum_affect_ads += len(affect_ads[ds])
        print(f"{ds}: {len(affect_ads[ds])}")
    print(f"sum_affect_ads: {sum_affect_ads}")


def query_intervention_result(start_ds, end_ds, prod=False):
    """ 查询MySQL输出结果
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    print(current_dir)
    conn = pymysql.connect(user=os.getenv("DB_USER"), password=os.getenv("DB_PASSWORD"), 
                           host=os.getenv("DB_HOST"), port=int(os.getenv("DB_PORT")), db=os.getenv("DB_NAME"))
    with conn.cursor() as cursor:    
        query = f"select ad_id, action_taken, action_reason, ds FROM ad_intervention_history where ds between '{start_ds}' and '{end_ds}'"
        cursor.execute(query)
        result = cursor.fetchall()
    conn.close()
    result_df = pd.DataFrame(result, columns=["ad_id", "action_taken", "action_reason", "ds"])
    return result_df


def load_td_data(filepath, start_ds=None, end_ds=None):
    """ 从文件中加载TradeDesk数据 """
    if type(filepath) == str:
        with open(filepath, 'rb') as f:
            new_fetched_data = pickle.load(f)
    elif type(filepath) == list:
        new_fetched_data = {}
        for fp in filepath:
            with open(fp, 'rb') as f:
                new_fetched_data.update(pickle.load(f))
    if start_ds is None or end_ds is None:
        return new_fetched_data
    return {ds: new_fetched_data[ds] for ds in pd.date_range(start=start_ds, end=end_ds)}


def save_td_data(filepath: str, td_data: dict):
    """ 保存TradeDesk数据到文件 """
    with open(filepath, 'wb') as f:
        pickle.dump(td_data, f)
    
    

def load_sdk_package_data_from_impala(start_ds: str, end_ds: str, adtrace_aid_list: List[int], adtrace_platform_list: List[str], _config: AdConfig) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """ 从impala中获取sdk包数据
    return: 账号ID与sdk包的对应关系, sdk包的最早创建日期
    """
    conn = connect(
        host=_config['impala']['proxy_host'],
        port=_config['impala']['proxy_port'],
        auth_mechanism="NOSASL"
    )
    with conn.cursor(user=_config['impala']['user']) as cursor:
        cursor.execute("refresh koi_data.dwd_advertisement_feature_20461")
        query = """
        select distinct channel_ty_account_id, sdk_package_name, adtrace_platform
        from koi_data.dwd_advertisement_feature_20461 
        where attribution_day between '{start_ds}' and '{end_ds}' 
            and channel_ty_account_id is not null 
            and cast(adtrace_aid as int) in ({adtrace_aid_list})
            and adtrace_platform in ({adtrace_platform_list})
        """.format(start_ds=start_ds, end_ds=end_ds,
                   adtrace_aid_list=','.join(map(str, adtrace_aid_list)), 
                   adtrace_platform_list="'" + "','".join(adtrace_platform_list) + "'")
        cursor.execute(query)
        result = cursor.fetchall()
        
        query_2 = """
        select sdk_package_name, min(attribution_day) as min_attribution_day
        from koi_data.dwd_advertisement_feature_20461 
        where attribution_day between '{start_ds}' and '{end_ds}' 
            and channel_ty_account_id is not null 
            and cast(adtrace_aid as int) in ({adtrace_aid_list})
            and adtrace_platform in ({adtrace_platform_list})
        group by sdk_package_name
        """.format(start_ds=start_ds, end_ds=end_ds,
                   adtrace_aid_list=','.join(map(str, adtrace_aid_list)), 
                   adtrace_platform_list="'" + "','".join(adtrace_platform_list) + "'")
        cursor.execute(query_2)
        result_2 = cursor.fetchall()
        
    return pd.DataFrame(result, columns=['channel_ty_account_id', 'sdk_package_name', 'adtrace_platform']), pd.DataFrame(result_2, columns=['sdk_package_name', 'min_attribution_day'])


def load_operation_data_from_impala(start_ds: str, end_ds: str) -> pd.DataFrame:
    """ 从impala中获取优化师操作数据 """
    # TODO
    


def load_operation_data_from_file(filepath: str) -> pd.DataFrame:
    """ 从文件中获取优化师操作数据 """
    print(filepath)
    df = pd.read_csv(filepath,on_bad_lines='skip')
    print(df.shape)
    
    df['operation_time'] = df['operation_time'].str.strip()
    df = df[(df['operation_target'] == '广告') & (~df['operation_log'].isna())]
    df = df[
        (~df['operation_log'].str.contains("审核")) &
        (~df['operation_log'].str.contains("产品卖点")) &
        (~df['operation_log'].str.contains("修改 素材状态"))
    ]
    print(df.shape)
    
    def get_roi_change(log):
        if isinstance(log, str) and '广告ROI系数:' in log:
            # 提取数字
            numbers = re.findall(r'(\d+\.\d+)', log)
            if len(numbers) >= 2:
                old_roi = float(numbers[0])
                new_roi = float(numbers[1])
                if new_roi > old_roi:
                    return "提高ROI系数"
                elif new_roi < old_roi:
                    return "降低ROI系数"
        return None
    
    def get_bid_change(log):
        if isinstance(log, str) and '广告出价:' in log:
            # 提取数字
            numbers = re.findall(r'(\d+\.\d+)', log)
            if len(numbers) >= 2:
                old_bid = float(numbers[0])
                new_bid = float(numbers[1])
                if new_bid > old_bid:
                    return "提高出价"
                elif new_bid < old_bid:
                    return "降低出价"
        return None
    
    def get_budget_change(log):
        if isinstance(log, str) and '广告预算:' in log:
            # 提取数字
            numbers = re.findall(r'(\d+\.\d+)', log)
            if len(numbers) >= 2:
                old_budget = float(numbers[0])
                new_budget = float(numbers[1])
                if new_budget > old_budget:
                    return "提高预算"
                elif new_budget < old_budget:
                    return "降低预算"
        return None
    
    def get_action(log):
        if "修改 广告ROI系数" in log:
            return "修改ROI系数", get_roi_change(log)
        elif "修改 广告出价" in log:
            return "修改出价", get_bid_change(log)
        elif "修改 广告预算" in log:
            return "修改预算", get_budget_change(log)
        elif "修改 操作状态: 正常 -> 暂停" in log:
            return "暂停广告", None
        elif "修改 操作状态: 暂停 -> 正常" in log:
            return "重启广告", None
        else:
            return None, None
    
    df['action'] = df['operation_log'].apply(get_action)
    return df


if __name__ == "__main__":
    project_path = str(Path(__file__).parent.parent.parent)
    df = load_operation_data_from_file(os.path.join(project_path, "data/operation_logs_20250519.csv"))
    print(df.head())
    print(df.columns)
    print(df.dtypes)
    print(df.shape)
    print(df['action'].value_counts())
    