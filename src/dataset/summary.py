import sys
import pathlib
sys.path.append(str(pathlib.Path(__file__).parent.parent))

import os
import yaml
from tqdm import tqdm
from collections import defaultdict
from datetime import datetime, timedelta
import pymysql
from typing import Dict
import pandas as pd
from config import AdConfig
from dataset.request import TradeDeskManager
from dataset.misc import count_affect_ads, query_intervention_result


class Summary:
    def __init__(self, start_ds='2025-05-14', end_ds=datetime.now().strftime('%Y-%m-%d'), platform_id='1', project_id='2', device_os=1, config_file=None):
        season_1 = {1824474736139273:'2025-05-14',1824543668716761:'2025-05-14',1817948024763419:'2025-05-14',1810972857115659:'2025-05-14',1817948034574548:'2025-05-14',1817948028028179:'2025-05-14',1829643855469379:'2025-05-14',1829643849875011:'2025-05-14',1810972234232899:'2025-05-14',1817948041627993:'2025-05-14',1817948023230681:'2025-05-14'}
        season_2 = {****************:'2025-05-28',****************:'2025-05-28',****************:'2025-05-28',****************:'2025-05-28'}
        season_3 = {****************:'2025-06-11',****************:'2025-06-11',****************:'2025-06-11',****************:'2025-06-11',****************:'2025-06-11',****************:'2025-06-11',****************:'2025-06-11',****************:'2025-06-11'}
        self.config = AdConfig(config_file)
        self.start_ds = start_ds
        self.end_ds = end_ds
        self.account_create = {**season_1, **season_2, **season_3}
        self.td_manager = TradeDeskManager(self.config)
        
    def run(self):
        print(count_affect_ads(start_ds='2025-06-11', end_ds='2025-06-17', account_create=self.account_create))
        self.td_manager.fetch_td_data_without_download_to_db(self.start_ds, self.end_ds, platform_id=self.platform_id, project_id=self.project_id, device_os=self.device_os)
    
    
if __name__ == '__main__':
    summary = Summary(config_file='config-prod-3d.yaml')
    summary.run()
    


        