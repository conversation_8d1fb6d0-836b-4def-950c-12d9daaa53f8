import os
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import io
import time
import json
import pymysql
import pytz
import requests
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import create_engine
import traceback
from typing import Set
from deprecated import deprecated
from config import AdConfig


class TradeDeskManager:
    def __init__(self, _config: AdConfig=None):
        self.config = _config
    
    
    def get_auth(self):
        """ TradeDesk API认证token """
        token = None
        url = os.getenv("TD_AUTH_URL")
        payload = json.dumps({
            "username": os.getenv("TD_USERNAME"),
            "password": os.getenv("TD_PASSWORD")
        })

        response = requests.request("POST", url, data=payload)

        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                token = data.get("data", {}).get("token")
            else:
                print("Authentication failed:", data.get("message"))
        else:
            print("Request failed with status code:", response.status_code)
        response.raise_for_status()

        return token



    def fetch_td_data_without_download_to_db(self, date_start: datetime, date_end: datetime,  
                                             column_list: list, max_retries: int, platform: str, project: str) -> pd.DataFrame:
        """ 查询TradeDesk数据 
        Args:
            date_start: 开始日期
            date_end: 结束日期
            column_list: 列名列表
            max_retries: 最大重试次数
            platform: 渠道，"1": 今日头条, "2": 快手, "3": 广点通
            project: 项目，"2": 3D捕鱼
        Returns:
            df: TradeDesk数据
        """
        url = os.getenv("TD_URL")
        token = self.get_auth()
        headers = {
            'Authorization': token,
            'Content-Type': 'application/json'
        }
        
        delta_day = (date_end - date_start).days
        date_start = date_start.strftime('%Y-%m-%d')
        date_end = date_end.strftime('%Y-%m-%d')
        date_update = datetime.now().date().strftime('%Y-%m-%d')

        payload = json.dumps({
            "page": 1,
            "page_size": 20,
            "start_date": date_start,
            "end_date": date_end,
            "order_by": "ad_id",
            "order_type": "desc",
            "dimension": 9,
            "income_type": 2,
            "platform": [platform],
            "project": [project],
            "column_list": column_list,
            "dimension_list": [9, 11]  # 账户ID, 广告ID
        })

        try:
            if delta_day < 2:
                response = requests.request("POST", url, headers=headers, data=payload)
                response.raise_for_status()
            else:
                post_response = requests.request("POST", url, headers=headers, data=payload)
                post_response.raise_for_status()

                mission_id = post_response.json()['data']['id']
                mission_url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/async/v1/tasks/{}/".format(mission_id)

                data = None
                retries = 0
                while not data:
                    if retries >= max_retries:
                        raise TimeoutError(f"Data fetch timeout after {max_retries} attempts.")

                    data = requests.request("GET", mission_url, headers=headers).json()['data']
                    time.sleep(1)

                download_url = data['url']
                download_headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0'
                }
                response = requests.request("GET", download_url, headers=download_headers, timeout=30)

            bytes_io = io.BytesIO(response.content)
            df_columns = ['account_id', 'ad_id'] + column_list
            df = pd.read_excel(bytes_io)
            
            # 检查DataFrame是否为空或者列数不匹配
            if df.empty:
                print(f"No data found for {date_start} to {date_end}")
                return pd.DataFrame()
                
            # 检查列数是否匹配
            if len(df.columns) != len(df_columns):
                print(f"Column mismatch: expected {len(df_columns)} columns, got {len(df.columns)}")
                print(f"Actual columns: {list(df.columns)}")
                print(f"Expected columns: {df_columns}")
                return pd.DataFrame()
                
            df.columns = df_columns
            df = df[(df['ad_id'] != 0) & (df['ad_id'] != '未知')]
            df['date_start'] = date_start
            df['date_end'] = date_end
            df['date_update'] = date_update
            df['platform_id'] = platform
            
        except requests.exceptions.RequestException as e:
            print(f"HTTP request failed: {e}")
            print(traceback.format_exc())
            return pd.DataFrame()

        except TimeoutError as e:
            print(f"Timeout error: {e}")
            print(traceback.format_exc())
            return pd.DataFrame()

        except Exception as e:
            print(f"Unexpected error: {e}")
            print(traceback.format_exc())
            return pd.DataFrame()

        return df
    
        
        
    def fetch_media_data(self, platform:str, project_id: int, start_ds:str, end_ds:str, scope:str, page_size:int=100, object_id:str=None, device_os:int=None, column_list:list=None):
        assert scope in ['advertiser', 'campaign', 'ad']
        if platform == 'jrtt':
            if scope == 'advertiser':
                url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/automation/v1/jrtt/advertiser/data/"
                column_list = column_list or ["advertiser_name","advertiser_id","remark","studio_name","project_display","device_os","optimizer_name","daily_budget","show","click","cost","cpm","download_finish_cnt","install_finish_cnt","active","active_cost","active_register","active_register_cost","attribution_next_day_open_cnt","attribution_next_day_open_rate","active_pay","active_pay_cost","active_pay_rate","game_pay_count","game_pay_cost","active_pay_intra_day_count","active_pay_intra_day_cost","active_pay_intra_day_rate","attribution_game_pay_7d_count","attribution_game_pay_7d_cost","attribution_game_pay_7d_rate","attribution_game_in_app_ltv_1day","attribution_game_in_app_ltv_2days","attribution_game_in_app_ltv_3days","attribution_game_in_app_ltv_4days","attribution_game_in_app_ltv_5days","attribution_game_in_app_ltv_6days","attribution_game_in_app_ltv_7days","attribution_game_in_app_ltv_8days","attribution_game_in_app_roi_1day","attribution_game_in_app_roi_2days","attribution_game_in_app_roi_3days","attribution_game_in_app_roi_4days","attribution_game_in_app_roi_5days","attribution_game_in_app_roi_6days","attribution_game_in_app_roi_7days","attribution_game_in_app_roi_8days"]
            elif scope == 'campaign':
                url = 'https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/automation/v1/jrtt/project/data/'
                column_list = column_list or ["opt_status","jrtt_project_id","delivery_mode","advertiser_name","advertiser_id","project_display","device_os","optimizer_name","project_create_time","project_modify_time","jrtt_project_status","delivery_type","landing_type","marketing_goal","ad_type","delivery_product","inventory_type","optimization_goal","deep_optimization_goal","deep_optimization_type","bid","cpa_bid","deep_cpa_bid","roi_goal","first_roi_goal","start_time","end_time","bid_type","daily_budget","budget_mode","show","click","cost","cpm","download_finish_cnt","install_finish_cnt","active","active_cost","active_register","active_register_cost","attribution_next_day_open_cnt","attribution_next_day_open_rate","active_pay","active_pay_cost","active_pay_rate","game_pay_count","game_pay_cost","active_pay_intra_day_count","active_pay_intra_day_cost","active_pay_intra_day_rate","attribution_game_pay_7d_count","attribution_game_pay_7d_cost","attribution_game_pay_7d_rate","attribution_game_in_app_ltv_1day","attribution_game_in_app_ltv_2days","attribution_game_in_app_ltv_3days","attribution_game_in_app_ltv_4days","attribution_game_in_app_ltv_5days","attribution_game_in_app_ltv_6days","attribution_game_in_app_ltv_7days","attribution_game_in_app_ltv_8days","attribution_game_in_app_roi_1day","attribution_game_in_app_roi_2days","attribution_game_in_app_roi_3days","attribution_game_in_app_roi_4days","attribution_game_in_app_roi_5days","attribution_game_in_app_roi_6days","attribution_game_in_app_roi_7days","attribution_game_in_app_roi_8days"]
            else:
                url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/automation/v1/jrtt/promotion/data"
                column_list = column_list or ["opt_status","jrtt_promotion_name","jrtt_promotion_id","delivery_mode","promotion_create_time","promotion_modify_time","learning_phase","jrtt_promotion_status","aweme_id","daily_budget","budget_mode","bid","cpa_bid","deep_cpa_bid","roi_goal","first_roi_goal","schedule_time","show","click","cost","cpm","download_finish_cnt","install_finish_cnt","active","active_cost","active_register","active_register_cost","attribution_next_day_open_cnt","attribution_next_day_open_rate","active_pay","active_pay_cost","active_pay_rate","game_pay_count","game_pay_cost","active_pay_intra_day_count","active_pay_intra_day_cost","active_pay_intra_day_rate","attribution_game_pay_7d_count","attribution_game_pay_7d_cost","attribution_game_pay_7d_rate","attribution_game_in_app_ltv_1day","attribution_game_in_app_ltv_2days","attribution_game_in_app_ltv_3days","attribution_game_in_app_ltv_4days","attribution_game_in_app_ltv_5days","attribution_game_in_app_ltv_6days","attribution_game_in_app_ltv_7days","attribution_game_in_app_ltv_8days","attribution_game_in_app_roi_1day","attribution_game_in_app_roi_2days","attribution_game_in_app_roi_3days","attribution_game_in_app_roi_4days","attribution_game_in_app_roi_5days","attribution_game_in_app_roi_6days","attribution_game_in_app_roi_7days","attribution_game_in_app_roi_8days"]
        elif platform == 'kuaishou':
            if scope == 'advertiser':
                url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/automation/v1/kuaishou/advertiser/data/"
                column_list = column_list or ["advertiser_name","advertiser_id","remark","studio_name","project_display","device_os","optimizer_name","daily_budget","show","aclick","bclick","cost","download_completed","download_installed","activation","activation_cost","event_register","register_cost","event_next_day_stay","next_day_stay_rate","event_new_user_pay","new_user_pay_cost","new_user_pay_rate","event_pay_first_day","event_pay_purchase_amount_first_day","roi_1","event_pay_purchase_amount_one_day_by_conversion","roi_one_day_by_conversion","event_pay_purchase_amount_three_day_by_conversion","roi_3","event_pay_purchase_amount_week_by_conversion","day_7","event_pay","event_pay_purchase_amount","roi"]
            elif scope == 'campaign':
                url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/automation/v1/kuaishou/campaign/data/"
                column_list = column_list or ["put_status","campaign_name","campaign_id","auto_manage","advertiser_name","advertiser_id","studio_name","project_display","device_os","optimizer_name","create_time","update_time","status","daily_budget","bid_type","auto_adjust","auto_build","show","aclick","bclick","cost","download_completed","download_installed","activation","activation_cost","event_register","register_cost","event_next_day_stay","next_day_stay_rate","event_new_user_pay","new_user_pay_cost","new_user_pay_rate","event_pay_first_day","event_pay_purchase_amount_first_day","roi_1","event_pay_purchase_amount_one_day_by_conversion","roi_one_day_by_conversion","event_pay_purchase_amount_three_day_by_conversion","roi_3","event_pay_purchase_amount_week_by_conversion","day_7","event_pay","event_pay_purchase_amount","roi"]
            else:
                url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/automation/v1/kuaishou/unit/data/"
                column_list = column_list or ["put_status","unit_name","unit_id","advertiser_name","advertiser_id","campaign_name","campaign_id","studio_name","project_display","device_os","optimizer_name","status","study_status","review_detail","create_time","update_time","daily_budget","begin_time","end_time","schedule_time","scene_id","app_name","device_os_type","app_package_name","url","bid_type","cpa_bid","convert_id","ocpx_action_type","bid","deep_conversion_type","deep_conversion_bid","roi_ratio","jingle_bell_id","live_user_id","show","aclick","bclick","cost","download_completed","download_installed","activation","activation_cost","event_register","register_cost","event_next_day_stay","next_day_stay_rate","event_new_user_pay","new_user_pay_cost","new_user_pay_rate","event_pay_first_day","event_pay_purchase_amount_first_day","roi_1","event_pay_purchase_amount_one_day_by_conversion","roi_one_day_by_conversion","event_pay_purchase_amount_three_day_by_conversion","roi_3","event_pay_purchase_amount_week_by_conversion","day_7","event_pay","event_pay_purchase_amount","roi"]
        elif platform == 'gdt':
            if scope == 'advertiser':
                url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/automation/v1/gdt/advertiser/data/"
                column_list = column_list or ["advertiser_name","advertiser_id","remark","studio_name","project_display","device_os","optimizer_name","daily_budget","balance","bill_deposit_amount","wallet_name","wallet_balance","show","view_user_count","click","click_user_count","cost","acquisition_cost","cpm","download_count","install_count","active","active_cost","pay_count","active_pay_user_rate","first_pay_count","first_day_first_pay_count","first_day_first_pay_cost","first_day_pay_amount","first_day_pay_count","first_pay_cost","roi1","payment_amount_activated_d3","active_d3_pay_count","roi3","payment_amount_activated_d7","active_d7_pay_count","roi7","mini_game_register_users","mini_game_register_cost","mini_game_paying_amount_d1","mini_game_paying_users_d1","mini_game_roi1","mini_game_paying_amount_d3","mini_game_roi3","mini_game_paying_amount_d7","mini_game_roi7","mini_game24h_pay_amount","mini_game24h_roi","mini_game_purchase_amount","mini_game_pay_roi"]
            else:
                url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/automation/v1/gdt/ad_group/data/"
                column_list = column_list or ["configured_status","adgroup_name","adgroup_id","advertiser_name","advertiser_id","studio_name","project_display","device_os","optimizer_name","system_status","created_time","last_modified_time","daily_budget","bid_amount","begin_date","end_date","time_series","marketing_goal","marketing_sub_goal","marketing_carrier_type","marketing_target_type","site_set","optimization_goal","deep_conversion_goal","deep_conversion_expected_roi","deep_conversion_bid_amount","bid_mode","auto_acquisition_enabled","auto_acquisition_budget","auto_acquisition_status","cost_guarantee_status","automatic_site_enabled","show","view_user_count","click","click_user_count","cost","acquisition_cost","cpm","download_count","install_count","active","active_cost","pay_count","active_pay_user_rate","first_pay_count","first_day_first_pay_count","first_day_first_pay_cost","first_day_pay_amount","first_day_pay_count","first_pay_cost","roi1","payment_amount_activated_d3","active_d3_pay_count","roi3","payment_amount_activated_d7","active_d7_pay_count","roi7","mini_game_register_users","mini_game_register_cost","mini_game_paying_amount_d1","mini_game_paying_users_d1","mini_game_roi1","mini_game_paying_amount_d3","mini_game_roi3","mini_game_paying_amount_d7","mini_game_roi7","mini_game24h_pay_amount","mini_game24h_roi","mini_game_purchase_amount","mini_game_pay_roi"]
        else:
            raise ValueError(f"platform {platform} not supported")
        payload = {"page":1, "page_size":page_size,
                    "column_list": column_list,
                    "project_id": [project_id],"device_os":device_os, "sort_key":"cost","sort_type":"desc",
                    "start_date":None,"end_date":None}
        if object_id is not None:
            if platform == 'jrtt':
                if scope == 'campaign':
                    payload['jrtt_project_id'] = object_id
                elif scope == 'ad':
                    payload['promotion_id'] = object_id
                else:
                    payload['advertiser_id'] = [int(object_id)]
            elif platform == 'kuaishou':
                if scope == 'campaign':
                    payload['campaign_id'] = object_id
                elif scope == 'ad':
                    payload['unit_id'] = object_id
                else:
                    payload['advertiser_id'] = [int(object_id)]
            elif platform == 'gdt':
                if scope == 'advertiser':
                    payload['advertiser_id'] = [int(object_id)]
                else:
                    payload['adgroup_id'] = object_id
        token = self.get_auth()
        headers = {
            'Authorization': token,
            'Content-Type': 'application/json'
        }
        data = {}
        
        for ds in pd.date_range(start_ds, end_ds):
            daily_dfs = []
            payload["start_date"] = int(ds.timestamp() * 1000)
            payload["end_date"] = int(ds.timestamp() * 1000)
            payload['page'] = 0
            total = float('inf')
            while payload['page'] * page_size < total:
                payload['page'] += 1
                # print(payload)
                response = requests.post(url, headers=headers, json=payload)
                js = response.json()
                total = js['data']['total']
                df = pd.DataFrame(js['data']['data'])[column_list]
                if not df.empty:
                    daily_dfs.append(df)
            if daily_dfs:
                df = pd.concat(daily_dfs)
                df['ds'] = ds.strftime('%Y-%m-%d')
                data[ds.strftime('%Y-%m-%d')] = df
                
        return pd.concat(data.values())
        

    def get_unpaused_ad_ids(self, platform:str, project_id:int) -> Set[str]:
        df = self.fetch_media_data(platform=platform, project_id=project_id, start_ds=(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'), end_ds=datetime.now().strftime('%Y-%m-%d'), scope='ad')
        if platform == 'jrtt':
            return set(df[df['opt_status'] == 'ENABLE']['jrtt_promotion_id'].astype(str).unique())
        elif platform == 'kuaishou':
            return set(df[df['put_status'] == '投放中']['unit_id'].astype(str).unique())
        elif platform == 'gdt':
            return set(df[df['configured_status'] == "AD_STATUS_NORMAL"]['adgroup_id'].astype(str).unique())



@deprecated(reason="Use TradeDeskManager.fetch_td_data_without_download_to_db instead")
def fetch_td_data(date_start, date_end, max_retries=10):
    url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/report/v1/platform/group/download/"

    column_list = [
        "cost",
        "new_user",
        "pay1",
        "pay2",
        "pay3",
        "pay4",
        "pay5",
        "pay6",
        "pay7",
        "pay8",
        "pay9",
        "pay10",
        "pay11",
        "pay12",
        "pay13",
        "pay14",
        "pay15",
        "pay30",
        "pay45",
        "pay60",
        "pay90",
        "pay120",
        "pay150",
        "pay180",
        "pay210",
        "pay240",
        "pay270",
        "pay300",
        "pay360",
        "total_pay"
    ]
    delta_day = (date_end - date_start).days
    date_start = date_start.strftime('%Y-%m-%d')
    date_end = date_end.strftime('%Y-%m-%d')
    date_update = datetime.now().date().strftime('%Y-%m-%d')

    payload = json.dumps({
        "page": 1,
        "page_size": 20,
        "start_date": date_start,
        "end_date": date_end,
        "order_by": "ad_id",
        "order_type": "desc",
        "dimension": 9,
        "income_type": 2,
        "platform": ["1"],
        "project": ["2"],
        "column_list": column_list,
        "dimension_list": [9, 11]
    })

    headers = {
        'Authorization': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjozMjUsInVzZXJuYW1lIjoiemhvdXhpYW9xaXUiLCJpc3MiOiJhZC1tYW5hZ2VyIiwiZXhwIjoxNzQ1ODk3NDgzLCJuYmYiOjE3NDUyOTI2ODMsImlhdCI6MTc0NTI5MjY4M30.fTrS5uIF90nSeStlRiTi4ibXOj5b32QtLq5hbK-qT6c',
        'Content-Type': 'application/json'
    }

    try:
        if delta_day < 2:
            response = requests.request("POST", url, headers=headers, data=payload)
            response.raise_for_status()
        else:
            post_response = requests.request("POST", url, headers=headers, data=payload)
            post_response.raise_for_status()

            mission_id = post_response.json()['data']['id']
            mission_url = "https://pt09-tradedesk-online.tuyoo.com/api/ad-manager/async/v1/tasks/{}/".format(mission_id)

            data = None
            retries = 0
            while not data:
                if retries >= max_retries:
                    raise TimeoutError(f"Data fetch timeout after {max_retries} attempts.")

                data = requests.request("GET", mission_url, headers=headers).json()['data']
                time.sleep(1)

            download_url = data['url']
            download_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0'
            }
            response = requests.request("GET", download_url, headers=download_headers, timeout=30)

        bytes_io = io.BytesIO(response.content)
        df_columns = ['account_id', 'ad_id'] + column_list
        df = pd.read_excel(bytes_io)
        df.columns = df_columns
        df = df[(df['ad_id'] != 0) & (df['ad_id'] != '未知')]
        df['date_start'] = date_start
        df['date_end'] = date_end
        df['date_update'] = date_update

        engine = create_engine('mysql+pymysql://{}:{}@{}:{}/{}'.format(
            os.getenv("DB_USER"), os.getenv("DB_PASSWORD"), os.getenv("DB_HOST"), int(os.getenv("DB_PORT")), os.getenv("DB_NAME"),
        ))
        df.to_sql('ad_profile', engine, if_exists='append', index=False)

    except requests.exceptions.RequestException as e:
        print(f"HTTP request failed: {e}")
        return 1

    except TimeoutError as e:
        print(f"Timeout error: {e}")
        return 1

    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1

    return 0

@deprecated(reason="Use TradeDeskManager.fetch_td_data_without_download_to_db instead")
def run_fetch_td_data_daily(attribution_day=None, observed_days=3, predicted_days=7):
    if attribution_day is None:
        attribution_day = datetime.now(pytz.timezone('Asia/Shanghai')).date() - timedelta(days=observed_days)
    monthly_date_start = attribution_day - timedelta(days=30)

    run_flag = 0
    for i in range(7):
        data_day = attribution_day - timedelta(days=i)
        print("Fetching daily data for {} to {}".format(data_day, data_day))
        start = time.time()
        run_flag |= fetch_td_data(data_day, data_day)
        print("Daily data fetch time: {:.2f} seconds".format(time.time() - start))

    print("Fetching monthly data for {} to {}".format(monthly_date_start, attribution_day))
    start = time.time()
    run_flag |= fetch_td_data(monthly_date_start, attribution_day)
    print("Monthly data fetch time: {:.2f} seconds".format(time.time() - start))
    return run_flag


if __name__ == "__main__":
    proj_path = str(Path(__file__).parent.parent.parent)
    trade_desk_manager = TradeDeskManager(AdConfig(config_path=os.path.join(proj_path, 'config-dev-3d.yaml'), 
                                                   env_path=os.path.join(proj_path, '.env.prod')))
    df = trade_desk_manager.fetch_media_data("2025-07-01", "2025-07-01")
    print(df)