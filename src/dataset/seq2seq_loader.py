import sys
import pathlib
sys.path.append(str(pathlib.Path(__file__).parent.parent))

import os
import yaml
import argparse
import pickle
import calendar
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import numpy as np
import pandas as pd
# from pandarallel import pandarallel
# pandarallel.initialize(progress_bar=True, nb_workers=8)
import torch
from torch import nn
from torch.nn import functional as F
import pytorch_lightning as pl
from torch.utils.data import DataLoader, Dataset, random_split
from config import AdConfig
from dataset.misc import load_td_data
from dataset.dataloader import AdsDataset
from dataset.request import TradeDeskManager
  
    
class AdsDataModule(pl.LightningDataModule):
    def __init__(self, data_dir, platform_id: str, project: str,
                 start_ds_train:str, end_ds_train:str, start_ds_test:str, end_ds_test:str,
                 batch_size, cost_threshold, roi_threshold, max_seq_length, label_range, label_day, observe_day,
                 attribution_csv: str, click_data_csv: str,
                 val_split: float = 0.2, load_from_file: bool = True, mode: str = 'train', **kwargs):
        super().__init__()
        self.platform_id = platform_id
        self.platform_name = 'jrtt' if platform_id == '1' else 'kuaishou' if platform_id == '2' else 'gdt' if platform_id == '3' else 'unknown'
        self.project = project
        self.start_ds_train = start_ds_train
        self.end_ds_train = end_ds_train
        self.start_ds_test = start_ds_test
        self.end_ds_test = end_ds_test
        self.batch_size = batch_size
        self.val_split = val_split
        self.max_seq_length = max_seq_length
        self.cost_threshold = cost_threshold
        self.roi_threshold = roi_threshold
        self.data_dir = data_dir
        self.label_range = label_range
        self.label_day = label_day
        self.observe_day = observe_day
        self.load_from_file = load_from_file
        self.attribution_csv = attribution_csv
        self.click_data_csv = click_data_csv
        self.mode = mode
        self.fp_list = []
        self.td_data = {}
        if load_from_file:
            for year in range(int(min(start_ds_train, start_ds_test).split('-')[0]), 
                              int(max(end_ds_train, end_ds_test).split('-')[0]) + 1):
                for month in range(1, 13):
                    last_day = calendar.monthrange(year, month)[1]
                    start_ds = f'{year}-{month:02d}-01'
                    end_ds = f'{year}-{month:02d}-{last_day:02d}'
                    self.fp_list.append(os.path.join(data_dir, f'{self.platform_name}_td_data_{start_ds}_{end_ds}.pkl'))
        else:
            self.td_manager = TradeDeskManager(kwargs.get('global_config'))


    def setup(self, stage=None):
        if self.load_from_file:
            for fp in self.fp_list:
                if os.path.exists(fp):
                    with open(fp, 'rb') as f:
                        td_data = pickle.load(f)
                        self.td_data.update(td_data)
        else:
            feature_columns = ["cost", "show","click","cost_by_thousand_show","new_user","new_paid_user","new_user_cost","new_paid_user_cost",
                            "ltv1","roi1","cvr","ctr","convert","active_cost","convert_cost","active","ad_id_total",
                            "pay1","pay2","pay3","pay4","pay5","pay6","pay7","pay15","pay30","pay180",
                            "ltv2","ltv3","ltv4","ltv5","ltv6","ltv7","ltv15","ltv30","ltv180",
                            "roi2","roi3","roi4","roi5","roi6","roi7","roi15","roi30","roi90","roi180",
                            "stay_num2","stay_num3","stay_num4","stay_num5","stay_num6","stay_num7","stay_num15","stay_num30","stay_num90","stay_num180",
                            "ltv90","pay90","new_user_ad_trace","pay1_ad_trace","pay7_ad_trace", "total_pay"]
            if self.mode == 'train':
                _start_ds = min(self.start_ds_train, self.start_ds_test)
                _end_ds = max(self.end_ds_train, self.end_ds_test)
            else:
                _start_ds = self.start_ds_test
                _end_ds = self.end_ds_test
            for ds in pd.date_range(_start_ds, _end_ds):
                one_day_df = self.td_manager.fetch_td_data_without_download_to_db(ds, ds, 
                                                                                  column_list=feature_columns, max_retries=10,  
                                                                                  platform=self.platform_id, project=self.project)
                self.td_data[ds] = one_day_df
        if self.mode == 'train':
            self.td_data_train = {ds: self.td_data[ds] for ds in self.td_data if self.start_ds_train <= ds.strftime("%Y-%m-%d") <= self.end_ds_train}
            full_dataset = AdsDataset(self.td_data_train, data_dir=self.data_dir, 
                                        name=f'{self.platform_name}', 
                                        data_type='sequence', 
                                        label_range=self.label_range, 
                                        label_day=self.label_day, 
                                        observe_day=self.observe_day,
                                        roi_threshold=self.roi_threshold, 
                                        cost_threshold=self.cost_threshold, 
                                        max_seq_length=self.max_seq_length,
                                        attribution_result_csv=self.attribution_csv,
                                        click_data_csv=self.click_data_csv, 
                                        mode=self.mode)
            train_size = int((1 - self.val_split) * len(full_dataset))
            val_size = len(full_dataset) - train_size
            self.train_dataset, self.val_dataset = random_split(full_dataset, [train_size, val_size])
        else:
            self.train_dataset = self.val_dataset = []
        self.td_data_test = {ds: self.td_data[ds] for ds in self.td_data if self.start_ds_test <= ds.strftime("%Y-%m-%d") <= self.end_ds_test}
        self.test_dataset = AdsDataset(self.td_data_test, data_dir=self.data_dir, 
                                        name=f'{self.platform_name}', 
                                        data_type='sequence', 
                                        label_range=self.label_range, 
                                        label_day=self.label_day, 
                                        observe_day=self.observe_day,
                                        roi_threshold=self.roi_threshold, 
                                        cost_threshold=self.cost_threshold, 
                                        max_seq_length=self.max_seq_length,
                                        attribution_result_csv=self.attribution_csv,
                                        click_data_csv=self.click_data_csv,
                                        mode=self.mode)
        print(f"train_size: {len(self.train_dataset)}, val_size: {len(self.val_dataset)}, test_size: {len(self.test_dataset)}")


    def train_dataloader(self):
        return DataLoader(self.train_dataset, 
                          batch_size=self.batch_size, 
                          shuffle=True, 
                          num_workers=8, 
                          pin_memory=True,
                          collate_fn=self.collate_fn)

    def val_dataloader(self):
        return DataLoader(self.val_dataset, 
                          batch_size=self.batch_size, 
                          shuffle=False, 
                          num_workers=8,
                          pin_memory=True, 
                          collate_fn=self.collate_fn)

    def test_dataloader(self):
        return DataLoader(self.test_dataset, 
                          batch_size=self.batch_size, 
                          shuffle=False, 
                          num_workers=8, 
                          pin_memory=True,
                          collate_fn=self.collate_fn)
    
    def predict_dataloader(self):
        return DataLoader(self.test_dataset, 
                          batch_size=self.batch_size, 
                          shuffle=False, 
                          num_workers=8, 
                          pin_memory=True,
                          collate_fn=self.collate_fn)
        
    
    @staticmethod
    def collate_fn(batch):
        features = torch.stack([item['features'] for item in batch])
        labels = torch.stack([item['labels'] for item in batch])
        masks = torch.stack([item['mask'] for item in batch])
        ad_ds = [item['ad_ds'] for item in batch]
        ad_ids = [item['ad_id'] for item in batch]
        cost = torch.stack([item['cost'] for item in batch])
        pay7 = torch.stack([item['pay7'] for item in batch])
        
        # packages = [item['package_id'] for item in batch]
        # aids = [item['aid_id'] for item in batch]
        # acts = [item['act_id'] for item in batch]
        # proj_main_channel_ids = [item['proj_main_channel_id'] for item in batch]
        
        # return features, labels, masks, ad_ids, ad_ds, cost, pay7, packages, aids, acts, proj_main_channel_ids
        return features, labels, masks, ad_ids, ad_ds, cost, pay7
    

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--train_config', type=str, default='./seq2seq_config.yaml')
    args = parser.parse_args()
    with open(args.train_config, 'r') as f:
        train_config = yaml.safe_load(f)
    
    data_module = AdsDataModule(**train_config['data'])
    data_module.setup()
    
    train_dataloader = data_module.train_dataloader()
    for batch in train_dataloader:
        features, labels, masks, ad_ids, ad_ds, cost, pay7 = batch
        if pay7[0].any():
            print("ad_ids:", ad_ids, type(ad_ids))
            print("ad_ds:", ad_ds, type(ad_ds))
            print("features:", features.dtype, features.shape, features[0].tolist())
            print("cost:", cost.dtype, cost.shape, cost[0].tolist())
            print("pay7:", pay7.dtype, pay7.shape, pay7[0].tolist())
            print("masks:", masks.dtype, masks.shape, masks[0])
            print("labels:", labels.dtype, labels.shape, labels[0])
            break
            
    
