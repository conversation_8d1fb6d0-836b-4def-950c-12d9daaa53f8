import sys
import pathlib
sys.path.append(str(pathlib.Path(__file__).parent.parent))

import os
import pickle
import calendar
import time
import copy
from datetime import datetime, timedelta
from collections import Counter
from functools import reduce
from typing import Dict, List, Tuple, Optional, Union
import numpy as np
from numpy.lib.stride_tricks import sliding_window_view
import pandas as pd
import torch
from torch import nn
from torch.nn import functional as F
import pytorch_lightning as pl
from torch.utils.data import DataLoader, Dataset, random_split
from dataset.misc import load_td_data


class AdsDataset(Dataset):
    def __init__(self, td_data: Dict[str, pd.DataFrame], 
                 data_dir: str,
                 name: str,
                 observe_day: int, 
                 label_range: int,
                 label_day: int,
                 roi_threshold: float,
                 cost_threshold: float,
                 max_seq_length: int, 
                 data_type: str = 'sequence', 
                 task_type: str = 'classification',
                 attribution_result_csv: Union[List[str], str]  = '',
                 click_data_csv: Union[List[str], str]  = '',
                 verbose: bool = False,
                 mode: str = 'train'):
        """
        Args:
            td_data: 按日期存储的TradeDesk数据
            data_type: 生成表格类特征或序列类特征。
                表格类特征：基于广告从创建开始日计算，<observe_day>天内的数据作为特征
            task_type: 任务类型
            observe_day: 观察天数，从广告创建日期开始计算。在观察天数内的广告数据不进行分类
            label_range: 标签范围。未来<label_range>天内的<label_day>日ROI作为标签的定义源
            label_day: 标签天数
            roi_threshold: 标签定义的ROI阈值
            cost_threshold: 标签定义的消耗阈值
            seq_length: 序列最长长度
            verbose: 是否打印信息
            warm_up_days: 用于保证早期广告的创建天数计算的准确性
        """
        super().__init__()
        assert data_type in ['tabular', 'sequence'], 'data_type must be either tabular or sequence'
        assert task_type in ['classification', 'regression'], 'task_type must be either classification or regression'
        assert mode in ['train', 'eval'], 'mode must be either train or eval'
        self.name = name
        self.data_dir = data_dir
        self.verbose = verbose
        self.data_type = data_type
        self.observe_day = observe_day
        self.label_range = label_range
        self.label_day = label_day
        self.max_seq_length = max_seq_length
        self.task_type = task_type
        self.roi_threshold = roi_threshold
        self.cost_threshold = cost_threshold
        
        self.warm_up_days = 10
        self.start_ds = min(td_data.keys()).strftime("%Y-%m-%d")
        self.end_ds = max(td_data.keys()).strftime("%Y-%m-%d")
        if mode == 'train':
            self.feature_start_ds = (datetime.strptime(self.start_ds, "%Y-%m-%d") + timedelta(days=self.warm_up_days)).strftime("%Y-%m-%d")
            self.feature_end_ds = (datetime.strptime(self.end_ds, "%Y-%m-%d") - timedelta(days=self.label_range + self.label_day + self.observe_day)).strftime("%Y-%m-%d")
        else:
            self.feature_start_ds = self.start_ds
            self.feature_end_ds = self.end_ds
        self.ad_data = {}
        assert self.feature_start_ds <= self.feature_end_ds, 'feature_start_ds must be before feature_end_ds'
        
        ## load tradedesk metrics
        if self.data_type == 'tabular':
            raise NotImplementedError('Tabular data is not implemented yet')
            # ad_dead, ad_survive_observe, ad_survive_unknown = self.preprocess(td_data, data_type, self.feature_start_ds, self.feature_end_ds)
            # features_df, label_df = self.generate_feature(self.df, add_missing=False)
        else:
            if self._file_exists():
                self.ad_data = self._load_from_file()
            else:
                seq_df = self.preprocess(td_data, data_type, self.feature_start_ds, self.feature_end_ds)
                self.ad_data = self.generate_feature_sequence(seq_df)
                self._save_to_file(self.ad_data)
            self.ad_ids = list(self.ad_data.keys())
        
        ## load adtrace data
        # self.ad_meta, self.ad2devices, self.campaign_create_at, self.account_create_at, self.package_create_at = self.process_adtrace_data(attribution_result_csv, click_data_csv)
        # device_list = list(set(reduce(lambda x, y: x + y, list(self.ad2devices.values()))))
        # aid_list = list(set([self.ad_meta_info[ad]['adtrace_aid'] for ad in self.ad_meta_info]))
        # act_list = list(set([self.ad_meta_info[ad]['adtrace_act_name'] for ad in self.ad_meta_info]))
        # proj_main_channel_list = list(set([self.ad_meta_info[ad]['proj_main_channel'] for ad in self.ad_meta_info]))
        # self.package2id = {pkg: i for i, pkg in enumerate(list(set(self.ad2package.values())))}
        # self.device2id = {device: i for i, device in enumerate(device_list)}
        # self.aid2id = {aid: i for i, aid in enumerate(aid_list)}
        # self.act2id = {act: i for i, act in enumerate(act_list)}
        # self.proj_main_channel2id = {proj: i for i, proj in enumerate(proj_main_channel_list)}
        
        
    def preprocess(self, td_data: dict, data_type: str, feature_start_ds: str, feature_end_ds: str):
        td_df = pd.concat([td_data[ds] for ds in td_data])
        td_df['account_id'] = td_df['account_id'].astype(str)
        td_df['ad_id'] = td_df['ad_id'].astype(str)
        td_df['dt'] = pd.to_datetime(td_df['date_start'])
        td_df['create_ds'] = td_df.groupby('ad_id')['date_start'].transform('min')
        td_df['create_dt'] = pd.to_datetime(td_df['create_ds'])
        td_df['delay'] = (td_df['dt'] - td_df['create_dt']).dt.days
        td_df['max_delay'] = td_df.groupby('ad_id')['delay'].transform('max')
        valid_df = td_df[(td_df['create_dt'] >= pd.to_datetime(feature_start_ds)) & (td_df['create_dt'] <= pd.to_datetime(feature_end_ds)) & (td_df['ad_id'].str.len() > 2)]
        if self.verbose:
            print("valid df shape:", valid_df.shape)
            print("ad_id count:", valid_df['ad_id'].nunique())
        if data_type == 'sequence':
            valid_df = self._fill_missing_dates_per_group(valid_df)
            # valid_df = self._generate_label(valid_df)
            valid_df = self._shift_features(valid_df)
            return valid_df
        else:
            valid_ad_dead = valid_df[valid_df['max_delay'] < self.observe_day]
            valid_ad_survive_observe = valid_df[(valid_df['max_delay'] >= self.observe_day) & (valid_df['delay'] < self.observe_day)]
            valid_ad_survive_unknown = valid_df[(valid_df['max_delay'] >= self.observe_day) & (valid_df['delay'] >= self.observe_day)]
            return valid_ad_dead, valid_ad_survive_observe, valid_ad_survive_unknown
    
    
    def process_adtrace_data(self, attribution_result_csv, click_data_csv):
        """
        adtrace数据包含两项：ga_view.dwd_event_20461 中 event_id = 'adtrace_attribution_results' 和  event_id = 'adtrace_click_save_success'
           adtrace_attribution_results：归因数据，每个新增对应一行
           adtrace_click_save_success：媒体回传的点击数据
        """
        def col2index(d: dict, cols: List[str]):
            """ 将字典中的列转换为索引 """
            ret = copy.deepcopy(d)
            value_mapping = {c: {} for c in cols}
            for ad_id in d.keys():
                for c in cols:
                    if d[ad_id][c] not in value_mapping[c]:
                        value_mapping[c][d[ad_id][c]] = len(value_mapping[c])
                    ret[ad_id][c] = value_mapping[c][d[ad_id][c]]
            return ret
        
        dtype = {'channel_ty_adgroup_id': str, 'channel_ty_account_id': str, 'channel_ty_campaign_id': str, 'adtrace_aid': str}
        if isinstance(attribution_result_csv, list):
            attribution_df = pd.concat([pd.read_csv(os.path.join(self.data_dir, f), dtype=dtype) for f in attribution_result_csv])
        else:
            attribution_df = pd.read_csv(os.path.join(self.data_dir, attribution_result_csv), dtype=dtype)
        if isinstance(click_data_csv, list):
            click_df = pd.concat([pd.read_csv(os.path.join(self.data_dir, f), dtype=dtype) for f in click_data_csv])
        else:
            click_df = pd.read_csv(os.path.join(self.data_dir, click_data_csv), dtype=dtype)
        attribution_df.drop(columns=['sub_job_id', 'execute_date'], inplace=True)
        click_df.drop(columns=['sub_job_id', 'execute_date'], inplace=True)
        invalid_ad_ids = {0, '0', 'NaN', '', '__PROMOTION_ID__'}
        attribution_df = attribution_df[(~attribution_df['channel_ty_adgroup_id'].isin(invalid_ad_ids)) & (attribution_df['channel_ty_adgroup_id'].notna()) & (attribution_df['channel_ty_adgroup_id'].str.isdigit())]
        click_df = click_df[(~click_df['channel_ty_adgroup_id'].isin(invalid_ad_ids)) & (click_df['channel_ty_adgroup_id'].notna()) & (click_df['channel_ty_adgroup_id'].str.isdigit())]
        # print(attribution_df.head())
        # print(attribution_df.shape)
        # print(attribution_df.dtypes)
        # print(click_df.head())
        # print(click_df.shape)
        # print(click_df.dtypes)
        meta_columns = [
            'adtrace_aid', 'adtrace_act_name', 'adtrace_platform', 'channel_ty_campaign_id', 'channel_ty_account_id', 'proj_main_channel',  
            # 'proj_sub_channel', 'sdk_package_name'
        ]
        ad_meta_1 = click_df.drop_duplicates(subset=['channel_ty_adgroup_id'], keep='first').set_index('channel_ty_adgroup_id')
        ad_meta_2 = attribution_df.drop_duplicates(subset=['channel_ty_adgroup_id'], keep='first').set_index('channel_ty_adgroup_id')
        # ad_meta_1['sdk_package_name'] = ""
        ad_meta_1 = ad_meta_1[meta_columns]
        ad_meta_2 = ad_meta_2[meta_columns]
        ad_meta = ad_meta_1.combine_first(ad_meta_2)
        ad_meta['ios_or_android'] = ad_meta['proj_main_channel'].apply(lambda x: 'ios' if x == 'appStore' else 'android')
        ad_meta = ad_meta.to_dict(orient='index')
        ad_meta = col2index(ad_meta, meta_columns + ['ios_or_android'])
        
        campaign_create_at = attribution_df.groupby('channel_ty_campaign_id')['day'].min().to_dict()
        account_create_at = attribution_df.groupby('channel_ty_account_id')['day'].min().to_dict()
        package_create_at = attribution_df.groupby('sdk_package_name')['day'].min().to_dict()
        # ad2package = attribution_df.set_index('channel_ty_adgroup_id')['sdk_package_name'].to_dict()
        ad2devices = attribution_df[['channel_ty_adgroup_id', 'day', 'sdk_device_name']].groupby(['channel_ty_adgroup_id', 'day'])['sdk_device_name'].apply(list).to_dict()
        # print("ad_meta_info:", list(ad_meta_info.items())[:10])
        # print("campaign_create_at:", list(campaign_create_at.items())[:10])
        # print("account_create_at:", list(account_create_at.items())[:10])
        # print("package_create_at:", list(package_create_at.items())[:10])
        # print("ad2package:", list(ad2package.items())[:10])
        # print("ad2devices:", list(ad2devices.items())[:10])
        return ad_meta, ad2devices, campaign_create_at, account_create_at, package_create_at
    
    
    def __len__(self):
        return len(self.ad_ids)
    
    
    def __getitem__(self, idx):
        ad_id = self.ad_ids[idx]
        features, cost, pay7, create_ds = self.ad_data[ad_id]   
        future_cost = sliding_window_view(np.pad(cost, (0, self.label_range)), window_shape=self.label_range, axis=0).sum(axis=1)[1:]
        future_pay7 = sliding_window_view(np.pad(pay7, (0, self.label_range)), window_shape=self.label_range, axis=0).sum(axis=1)[1:]
        labels = (future_cost >= self.cost_threshold) & (future_pay7 / (future_cost + 1e-9) >= self.roi_threshold)
        
        # pad & truncate
        seq_len = features.shape[0]
        if seq_len < self.max_seq_length:
            pad_len = self.max_seq_length - seq_len
            padded_features = np.pad(features[:seq_len], ((0, pad_len), (0, 0)), mode='constant')
            padded_labels = np.pad(labels[:seq_len], (0, pad_len), mode='constant', constant_values=0)
            padded_cost = np.pad(cost[:seq_len], (0, pad_len), mode='constant', constant_values=0)
            padded_pay7 = np.pad(pay7[:seq_len], (0, pad_len), mode='constant', constant_values=0)
        else:
            padded_features = features[:self.max_seq_length]
            padded_labels = labels[:self.max_seq_length]
            padded_cost = cost[:self.max_seq_length]
            padded_pay7 = pay7[:self.max_seq_length]
            seq_len = self.max_seq_length
        
        mask = np.zeros(self.max_seq_length, dtype=np.bool_)
        mask[:min(seq_len, self.max_seq_length)] = True
        
        return {
            "features": torch.tensor(padded_features, dtype=torch.float32), 
            "labels": torch.tensor(padded_labels, dtype=torch.long), 
            "mask": torch.tensor(mask, dtype=torch.bool),
            "cost": torch.tensor(padded_cost, dtype=torch.float32),
            "pay7": torch.tensor(padded_pay7, dtype=torch.float32),
            "ad_id": ad_id,
            "ad_ds": create_ds, 
            # "package_id": self.package2id[self.ad2package[ad_id]],
            # "aid_id": self.ad_meta[ad_id]['adtrace_aid'],
            # "act_id": self.ad_meta[ad_id]['adtrace_act_name'],
            # "ios_or_android": self.ad_meta[ad_id]['ios_or_android'],
        }
    
       
    def _fill_missing_dates_per_group(self, df, id_col='ad_id', date_col='dt'):
        """
        为每个 ad_id 根据其自身的起止日期补全中间缺失的日期
        并保留原始特征列，数值型填充0，非数值型填充NaN
        """
        df[date_col] = pd.to_datetime(df[date_col])
        group_bounds = (
            df.groupby(id_col)[date_col]
            .agg(['min', 'max'])
            .rename(columns={'min': 'start_date', 'max': 'end_date'})
            .reset_index()
        )
        
        def _generate_date_ranges(row):
            return pd.date_range(row['start_date'], row['end_date'], name=date_col)

        group_bounds['dt'] = group_bounds.apply(_generate_date_ranges, axis=1)
        group_bounds = group_bounds.explode('dt')[[id_col, date_col]]
        
        filled_df = pd.merge(
            group_bounds,
            df,
            on=[id_col, date_col],
            how='left'
        )
        numeric_cols = df.select_dtypes(include=np.number).columns
        non_numeric_cols = df.columns.difference(numeric_cols).difference([id_col, date_col])
        filled_df[numeric_cols] = filled_df[numeric_cols].fillna(0)
        filled_df[non_numeric_cols] = filled_df[non_numeric_cols].fillna(np.nan)
        return filled_df

    
    def _shift_features(self, df):
        # pay X 平移 X-1 天
        shift_cols = [[]]
        for i in range(1, 7):
            _cols = [f'pay{i+1}', f'ltv{i+1}', f'roi{i+1}', f'stay_num{i+1}']
            shift_cols.append(_cols)
        shift_cols[6].append("pay7_ad_trace")
        for shift_day in range(len(shift_cols)):
            for col in shift_cols[shift_day]:
                df["shift."+col] = df.groupby('ad_id')[col].shift(shift_day).fillna(0)
        return df
    
    def _save_to_file(self, ad_data):
        with open(os.path.join(self.data_dir, f'ad_data_{self.name}_{self.start_ds}_{self.end_ds}.pkl'), 'wb') as f:
            pickle.dump(ad_data, f)
    
    def _load_from_file(self):
        with open(os.path.join(self.data_dir, f'ad_data_{self.name}_{self.start_ds}_{self.end_ds}.pkl'), 'rb') as f:
            return pickle.load(f)
        
    def _file_exists(self):
        return os.path.exists(os.path.join(self.data_dir, f'ad_data_{self.name}_{self.start_ds}_{self.end_ds}.pkl'))
            
        
    def generate_feature_sequence(self, ad_df) -> Dict[str, Tuple[np.ndarray, np.ndarray, np.ndarray, str]]:
        """ 使用广告的多日数据，拼接生成序列类特征 """
        ad_data = {}
        
        basic_features = ["cost", "show","click","cost_by_thousand_show","new_user","new_paid_user","new_user_cost","new_paid_user_cost", "cvr","ctr","convert","active_cost","convert_cost","active", "new_user_ad_trace"]
        pay_features = ['pay1'] + [f"shift.pay{i}" for i in range(2, 8)]
        ltv_features = ['ltv1'] + [f"shift.ltv{i}" for i in range(2, 8)]
        roi_features = ['roi1'] + [f"shift.roi{i}" for i in range(2, 8)]
        stay_features= [f"shift.stay_num{i}" for i in range(2, 8)]
        adtrace_features =["pay1_ad_trace", "shift.pay7_ad_trace"]
        scale = {"cost": 2000, "show": 100000, "click": 1000, "cost_by_thousand_show": 20, "new_user": 20, "new_user_cost": 200, "new_paid_user": 2000, 
                 "cvr": 0.05, "ctr": 0.05, "active_cost": 100, "convert_cost": 1000, "active": 20, "new_user_ad_trace": 5}
        for ad_id, group in ad_df.groupby('ad_id'):
            all_features = basic_features + pay_features + ltv_features + roi_features + stay_features + adtrace_features
            feat = group[all_features].values
            # label = (group['future_roi'].values >= self.roi_threshold) & (group['future_cost_sum'].values >= self.cost_threshold)
            # ad_data[ad_id] = (feat, group['future_cost_sum'].values, group['future_pay7_sum'].values, group['create_ds'].values[0])
            ad_data[ad_id] = (feat, group['cost'].values, group['pay7'].values, group['create_ds'].values[0])
            
        return ad_data
        
    
    def generate_feature(self, ad_df, ad_label_df, plot=False, add_missing=False, **kwargs):
        """ 使用广告的多日数据，拼接生成表格类特征 """
        ###  生成特征
        dfs = []
        for day in range(0, self.observe_day):
            basic_features = ["cost", "show","click","cost_by_thousand_show","new_user","new_paid_user","new_user_cost","new_paid_user_cost", "cvr","ctr","convert","active_cost","convert_cost","active"]
            pay_features = ["pay1","pay2","pay3","pay4","pay5","pay6","pay7"][:self.observe_day - day]
            ltv_features = ["ltv1", "ltv2","ltv3","ltv4","ltv5","ltv6","ltv7"][:self.observe_day - day]
            roi_features = ["roi1","roi2","roi3","roi4","roi5","roi6","roi7"][:self.observe_day - day]
            stay_features=["stay_num2","stay_num3","stay_num4","stay_num5","stay_num6","stay_num7"][:self.observe_day - day - 1]
            adtrace_features =["new_user_ad_trace","pay1_ad_trace","pay7_ad_trace"] if self.observe_day - day == 7 else ["new_user_ad_trace","pay1_ad_trace"]
            
            merge_features = basic_features + pay_features + ltv_features + roi_features + stay_features + adtrace_features
            day_df = ad_df[ad_df['delay'] == day][['ad_id'] + merge_features].copy()
            day_df.columns = ['ad_id'] + [f'day{day}.{cols}' for cols in merge_features]
            dfs.append(day_df)
        merged_df = dfs[0]
        for df in dfs[1:]:
            merged_df = pd.merge(merged_df, df, on='ad_id', how='outer')
        merged_df.fillna(0, inplace=True)
        ###  生成标签
        valid_ad_label_df = ad_label_df[(ad_label_df['delay'] >= self.observe_day) & (ad_label_df['delay'] < self.observe_day + self.label_range)]
        pay_cost_df = valid_ad_label_df.groupby('ad_id')[[f'pay{self.label_day}', 'cost']].sum()
        pay_cost_df.columns = ['future_pay', 'future_cost']
        pay_cost_df['future_roi'] = pay_cost_df['future_pay'] / (pay_cost_df['future_cost'] + 1e-8)
        if self.task_type == 'regression':
            noise = np.random.uniform(0, 0.16, size=len(pay_cost_df))
            alpha = 5
            pay_cost_df['label'] = (pay_cost_df['future_pay'] + noise * alpha) / (pay_cost_df['future_cost'] + alpha)
        else:
            # pay_cost_df['future_roi'] = (pay_cost_df['future_pay'] + np.random.uniform(0, 1)) / (pay_cost_df['future_cost'] + 1e-8)
            pay_cost_df['label'] = (pay_cost_df['future_roi'] >= self.roi_threshold) & (pay_cost_df['future_cost'] >= self.cost_threshold)
            # pay_cost_df['label'] = ~((pay_cost_df['future_roi'] < roi_threshold) & (pay_cost_df['future_cost'] >= cost_threshold))
            # pay_cost_df['label'] = (pay_cost_df['future_cost'] >= cost_threshold)
            # pay_cost_df['label'] = (pay_cost_df['future_roi'] > roi_threshold) | (pay_cost_df['future_cost'] >= cost_threshold)
            # pay_cost_df['label'] = (pay_cost_df['future_roi'] > roi_threshold) 
        
        if self.verbose:
            print('============ future_cost distribution:')
            print(pay_cost_df['future_cost'].describe(percentiles=np.arange(0.1, 1, 0.1)))
            print("============ future_roi distribution:")
            print(pay_cost_df['future_roi'].describe(percentiles=np.arange(0.1, 1, 0.1)))
            print("============ label distribution:")
            print(pay_cost_df['label'].describe(percentiles=np.arange(0.1, 1, 0.1)))
            print("y.shape:", pay_cost_df.shape)
            print("y_pos.shape:", pay_cost_df[pay_cost_df['label'] == True].shape)
            print("y_neg.shape:", pay_cost_df[pay_cost_df['label'] == False].shape)
        
            
        if add_missing:
            ids = pay_cost_df['ad_id'].unique()
            full_ids = merged_df['ad_id'].unique()
            missing_ids = full_ids[~np.isin(full_ids, ids)]
            print(f"missing: {len(missing_ids)}, total: {len(ids)}, full: {len(full_ids)}")
            missing_df = pd.DataFrame(
                [{
                    'future_pay': 0,
                    'future_cost': 0,
                    'future_roi': 0.0,
                    'label': False,
                    'ad_id': missing_ids[i]
                } for i in range(len(missing_ids))]
            )
            pay_cost_df = pd.concat([pay_cost_df, missing_df])
            print("X.shape:", merged_df.shape)
            print("y.shape:", pay_cost_df.shape)
        
        return merged_df, pay_cost_df
        

if __name__ == "__main__":
    start_ds = '2024-07-01'
    end_ds = '2025-05-31'
    platform_name = 'jrtt'
    data_dir='/mnt/data/xuyuhong/ad-intervention/data'
    fp_list = []
    for year in range(int(min(start_ds, end_ds).split('-')[0]), int(max(start_ds, end_ds).split('-')[0]) + 1):
        for month in range(1, 13):
            last_day = calendar.monthrange(year, month)[1]
            _start_ds = f'{year}-{month:02d}-01'
            _end_ds = f'{year}-{month:02d}-{last_day:02d}'
            fp_list.append(os.path.join(data_dir, f'{platform_name}_td_data_{_start_ds}_{_end_ds}.pkl'))
    td_data = {}
    for fp in fp_list:
        if os.path.exists(fp):
            with open(fp, 'rb') as f:
                td_data.update(pickle.load(f))
    td_data = {ds: td_data[ds] for ds in td_data if start_ds <= ds.strftime("%Y-%m-%d") <= end_ds}
    test_dataset = AdsDataset(
        td_data, 
        data_dir=data_dir, 
        name=platform_name, 
        data_type='sequence', 
        label_range=14, 
        label_day=7, 
        observe_day=1,
        roi_threshold=0.05, 
        cost_threshold=500, 
        max_seq_length=64,
        attribution_result_csv='adtrace_attribution_results_jrtt_3d_2023-10-01_2025-06-30.csv',
        click_data_csv=['click_save_jrtt_3d_2023-10-01_2024-09-30.csv', 'click_save_jrtt_3d_2024-10-01_2025-06-30.csv'],
    )