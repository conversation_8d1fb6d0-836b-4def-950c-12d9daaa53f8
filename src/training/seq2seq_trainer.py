import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import os
import numpy as np
import argparse
import yaml
from tqdm import tqdm
from dotenv import load_dotenv
import calendar
from datetime import datetime, timedelta
import pandas as pd
import torch
import pytorch_lightning as pl
from pytorch_lightning.loggers import TensorBoardLogger
from pytorch_lightning.strategies import DDPStrategy

from models.seq2seq import AdsTransformer
from dataset.dataloader import AdsDataset
from dataset.seq2seq_loader import AdsDataModule


def train(train_config):
    with open(train_config, 'r') as f:
        train_config = yaml.safe_load(f)
        
    data_module = AdsDataModule(**train_config['data'])
    data_module.setup()
    model = AdsTransformer(**train_config['model'])

    logger = TensorBoardLogger(
        save_dir=train_config['trainer']['logfile'],
        name=train_config['name'],
        version=f"v3-{datetime.now().strftime('%m%d-%H%M')}",
        default_hp_metric=False,
    )
    logger.experiment.add_text("description", str(train_config))

    trainer = pl.Trainer(
        max_epochs=train_config['trainer']['max_epochs'],
        accelerator=train_config['trainer']['accelerator'],
        devices=train_config['trainer']['devices'],
        strategy=train_config['trainer']['strategy'],
        # callbacks=[checkpoint_callback, early_stop_callback, print_callback],
        logger=logger,
        # val_check_interval=args.val_check_interval,
        log_every_n_steps=10,
        profiler="simple",
    )
    trainer.fit(model, datamodule=data_module)
    trainer.test(model, datamodule=data_module)
    
    
def evaluation(model_path, data_config, env_path, start_ds, end_ds):
    load_dotenv(dotenv_path=env_path)
    print(model_path)
    print(data_config)
    print(env_path)
    model = AdsTransformer.load_from_checkpoint(model_path)
    model.eval()
    data_config['start_ds_test'] = start_ds
    data_config['end_ds_test'] = end_ds
    data_config['load_from_file'] = True
    data_config['data_dir'] = '/mnt/data/xuyuhong/ad-intervention/data/'
    data_config['mode'] = 'eval'
    data_module = AdsDataModule(**data_config)
    data_module.setup()
    
    default_th = 0.3
    thresholds = [th for th in np.arange(0.1, 0.9, 0.1)]
    data = {th: {"cost_get": 0, "cost_discard": 0, "revenue_get": 0, "revenue_discard": 0} for th in thresholds}
    roi_lifetime = {d: {"cost": 0, "revenue": 0} for d in range(data_config['max_seq_length'])}
    roi_debias = {d: {"cost_get": 0, "revenue_get": 0, "cost_discard": 0, "revenue_discard": 0} for d in range(data_config['max_seq_length'])}
    ad_lifetime_get = {d: [] for d in range(data_config['max_seq_length'])}
    ad_lifetime_discard = {d: [] for d in range(data_config['max_seq_length'])}
    trainer = pl.Trainer(
        accelerator="gpu",
        devices=[0], 
    )
    
    result = trainer.predict(model, data_module)
    for item in tqdm(result):
        ad_ids, ad_ds, cost, revenue, probs = item['ad_ids'], item['ad_ds'], item['cost'], item['revenue'], item['probs']
        for i in range(len(ad_ids)):
            if ad_ds[i] < (datetime.strptime(start_ds, "%Y-%m-%d") + timedelta(days=10)).strftime("%Y-%m-%d"):
                continue
            
            ## 统计不同广告生命周期下的ROI     
            for d in roi_lifetime:
                if d < len(cost[i]):
                    roi_lifetime[d]["cost"] += cost[i][d].item()
                    roi_lifetime[d]["revenue"] += revenue[i][d].item()
                    
            ## 统计不同阈值下，保留和丢弃部分的ROI差异
            # for th in thresholds:
            #     data[th]["cost_get"] += cost[i][:3].sum().item()
            #     data[th]["revenue_get"] += revenue[i][:3].sum().item()
            #     for j in range(2, len(probs[i]) - 1):
            #         p = probs[i][j]
            #         if p >= th:
            #             data[th]["cost_get"] += cost[i][j+1].item()
            #             data[th]["revenue_get"] += revenue[i][j+1].item()
            #         else:
            #             data[th]["cost_discard"] += cost[i][j+1:].sum().item()
            #             data[th]["revenue_discard"] += revenue[i][j+1:].sum().item()
            #             break
            
            ## 对广告的天数纠偏
            l = [d for d in range(1, len(cost[i])) if probs[i][d] < default_th]
            stop_at = min(l) if l else len(cost[i])
            for d in range(len(roi_debias)):
                if d <= stop_at:
                    roi_debias[d]["cost_get"] += cost[i][d].item()
                    roi_debias[d]["revenue_get"] += revenue[i][d].item()
                    if d > 10: 
                        ad_lifetime_get[d].append((ad_ids[i], int(cost[i][d].item()), int(revenue[i][d].item()), stop_at))
                else:
                    roi_debias[d]["cost_discard"] += cost[i][d].item()
                    roi_debias[d]["revenue_discard"] += revenue[i][d].item()
                    if d > 10:
                        ad_lifetime_discard[d].append((ad_ids[i], int(cost[i][d].item()), int(revenue[i][d].item()), stop_at))
                    
    ## 统计不同广告生命周期下的ROI        
    data_list = []
    for d in roi_lifetime:
        data_list.append({
            "days": d,
            "cost": roi_lifetime[d]["cost"],
            "revenue": roi_lifetime[d]["revenue"],
            "roi": roi_lifetime[d]["revenue"] / (roi_lifetime[d]["cost"] + 1e-6)
        })
    df = pd.DataFrame(data_list).sort_values(by="days")
    print(df.to_string(index=False))
    
    ## 统计不同阈值下，保留和丢弃部分的ROI差异
    # data_list = []
    # for th in thresholds:
    #     data_list.append({
    #         "threshold": th,
    #         "cost_get": data[th]['cost_get'],
    #         "revenue_get": data[th]['revenue_get'],
    #         "cost_discard": data[th]['cost_discard'],
    #         "revenue_discard": data[th]['revenue_discard'],
    #         "roi_get": data[th]['revenue_get'] / (data[th]['cost_get'] + 1e-6),
    #         "roi_discard": data[th]['revenue_discard'] / (data[th]['cost_discard'] + 1e-6)
    #     })
    # df = pd.DataFrame(data_list)
    # print(df.to_string(index=False))
    
    ## 对广告的天数纠偏
    data_list = []
    for d in roi_debias:
        data_list.append({
            "days": d,
            "cost_get": roi_debias[d]["cost_get"],
            "revenue_get": roi_debias[d]["revenue_get"],
            "roi_get": roi_debias[d]["revenue_get"] / (roi_debias[d]["cost_get"] + 1e-6),
            "cost_discard": roi_debias[d]["cost_discard"],
            "revenue_discard": roi_debias[d]["revenue_discard"],
            "roi_discard": roi_debias[d]["revenue_discard"] / (roi_debias[d]["cost_discard"] + 1e-6),
            "roi_diff": roi_debias[d]["revenue_get"] / (roi_debias[d]["cost_get"] + 1e-6) - roi_debias[d]["revenue_discard"] / (roi_debias[d]["cost_discard"] + 1e-6)
        })
    df = pd.DataFrame(data_list)
    print(df.to_string(index=False))
    
    ## 查看后期广告分类不佳的问题
    for d in ad_lifetime_get:
        ad_lifetime_get[d].sort(key=lambda x: x[1], reverse=True)
        print(f"{d}", ad_lifetime_get[d][:10])
    for d in ad_lifetime_discard:
        ad_lifetime_discard[d].sort(key=lambda x: x[1], reverse=True)
        print(f"{d}", ad_lifetime_discard[d][:10])
    
            
if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--eval', action='store_true', default=False)
    parser.add_argument('--train_config', type=str, default='./seq2seq_config.yaml')
    # parser.add_argument('--model_path', type=str, default="/mnt/data/xuyuhong/ad-intervention/models/v3-0707-1035/checkpoints/epoch=55-step=7168.ckpt")
    parser.add_argument('--model_path', type=str, default="/mnt/data/xuyuhong/ad-intervention/logs/seq2seq/v3-0708-1545/checkpoints/epoch=99-step=25600.ckpt")
    # parser.add_argument('--model_path', type=str, default="/mnt/data/xuyuhong/ad-intervention/logs/seq2seq/v3-0708-0049/checkpoints/epoch=99-step=25600.ckpt")
    parser.add_argument('--start_ds', type=str, default='2025-01-01')
    parser.add_argument('--end_ds', type=str, default='2025-05-30')
    parser.add_argument('--env_path', type=str, default='.env.prod')
    args = parser.parse_args()
    if args.eval:
        with open(args.train_config, 'r') as f: 
            train_config = yaml.safe_load(f)
        evaluation(model_path=args.model_path, data_config=train_config['data'], env_path=args.env_path, start_ds=args.start_ds, end_ds=args.end_ds)
    else:
        train(train_config=args.train_config)
    