import yaml
import os
from dotenv import load_dotenv

class AdConfig:
    def __init__(self, config_path, env_path=""):
        if os.getenv("TD_URL") is None:
            load_dotenv(dotenv_path=env_path)
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
            
    def __getitem__(self, key):
        return self.config[key] if key in self.config else None
    
    def __contains__(self, key):
        return key in self.config
    
