# 环境配置
env: "prod"
db_name: "ad_automation_10010"
ga_project_id: "6" # Genesis GA 网站上的ID

path:
  log_path: "logs/ad_intervention_doudizhu.log"

# 规则/模型配置
jobs:
  - name: "jrtt_cbc_v20250729_doudizhu" # 任务名称
    class_path: "models.cbc.CBC" # 模型类路径
    log_file_path: "logs/jrtt_cbc_v20250729_doudizhu.log" # 日志文件路径
    model_path: "models/jrtt_cbc_v20250729_doudizhu" # 模型文件路径
    platform: "1" # 指广告平台， 1 为jrtt， 2为快手， 3为广点通
    project: "3"  # 斗地主项目ID
    at: "11:00" # 执行时间 在写入mysql后，11:30希德那边的脚本会执行，扫描表中的结果执行操作
